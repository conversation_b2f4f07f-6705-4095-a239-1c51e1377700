import { FutuWebSocket } from './futu-api-ts/src/index.js';

let websocket = new FutuWebSocket();

websocket.start("127.0.0.1", 33333, false, "232df6441fb2bbdd");

// 港股
const market = 1;
// 股票代码
const code = "08406";

websocket.onlogin = async (ret: boolean, msg: any) => {
    if (ret) {
        await testApi();

        // //关闭行情连接，连接不再使用之后，要关闭，否则占用不必要资源
        // //同时OpenD也限制了最多128条连接
        // //也可以一个页面或者一个项目维护一条连接，这里范例请求一次创建一条连接
        // websocket.stop();
    } else {
        // this.errMsg = "error: 请检查是否有设置store.js中key字段";
        console.log("登录失败:", msg);
    }
};

websocket.onPush = (cmd: number, res: any) => {
    console.log("推送消息", cmd);
    console.log(JSON.stringify(res, null, 4));
};

const testApi = async () => {
    // 股票快照
    const res = await websocket.GetSecuritySnapshot({ c2s: { securityList: [{ code, market }] } });
    // console.log("获取快照结果", JSON.stringify(res, null, 4));

    // 批量监听: 基础报价、摆盘、逐笔、分时
    let results = await websocket.Sub({ c2s: { securityList: [{ market, code }], subTypeList: [1, 2, 4, 5], isSubOrUnSub: true, isRegOrUnRegPush: true } });
    console.log("订阅基础报价结果", results);

    // // 单独监听: 基础报价
    // let res2 = await websocket.Sub({ c2s: { securityList: [{ market, code }], subTypeList: [1], isSubOrUnSub: true, isRegOrUnRegPush: true } });
    // console.log("订阅基础报价结果", res2);

    // // 单独监听: 摆盘
    // let res3 = await websocket.Sub({ c2s: { securityList: [{ market, code }], subTypeList: [2], isSubOrUnSub: true, isRegOrUnRegPush: true } });
    // console.log("订阅摆盘结果", res3);

    // // 单独监听: 逐笔
    // let res4 = await websocket.Sub({ c2s: { securityList: [{ market, code }], subTypeList: [4], isSubOrUnSub: true, isRegOrUnRegPush: true } });
    // console.log("订阅逐笔结果", res4);

    // // 单独监听: 分时
    // let res5 = await websocket.Sub({ c2s: { securityList: [{ market, code }], subTypeList: [5], isSubOrUnSub: true, isRegOrUnRegPush: true } });
    // console.log("订阅分时结果", res5);
};
