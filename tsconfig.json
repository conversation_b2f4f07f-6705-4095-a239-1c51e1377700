{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "Node", "lib": ["ES2022", "DOM"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "sourceMap": true, "allowSyntheticDefaultImports": true, "isolatedModules": true}, "include": ["*.ts", "futu-api-ts/src/**/*"], "exclude": ["node_modules", "dist", "futu-api-ts/node_modules"]}