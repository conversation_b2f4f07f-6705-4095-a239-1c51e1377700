{"name": "futu-api-ts-example", "version": "1.0.0", "description": "Futu API TypeScript 使用示例", "type": "module", "scripts": {"dev": "tsx main.ts", "simple": "tsx simple-example.ts", "main-simple": "tsx main-simple.ts", "build": "tsc", "start": "node dist/main.js"}, "dependencies": {"futu-api": "^9.3.5308", "long": "^5.2.3", "protobufjs": "^7.2.5", "bytebuffer": "^5.0.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/bytebuffer": "^5.0.0", "typescript": "^5.0.0", "tsx": "^4.0.0"}}