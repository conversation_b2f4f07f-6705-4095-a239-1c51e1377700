import { test } from 'node:test';
import assert from 'node:assert';
import { ftCmdID } from '../constants.js';

test('ftCmdID should contain expected commands', () => {
  assert.ok(ftCmdID);
  assert.ok(ftCmdID.TrdGetAccList);
  assert.strictEqual(ftCmdID.TrdGetAccList.cmd, 2001);
  assert.strictEqual(ftCmdID.TrdGetAccList.name, 'Trd_GetAccList');
});

test('ftCmdID should have all required trading commands', () => {
  const requiredCommands = [
    'TrdGetAccList',
    'TrdUnlockTrade',
    'TrdGetFunds',
    'TrdGetPositionList',
    'TrdPlaceOrder',
  ];

  for (const cmd of requiredCommands) {
    const cmdInfo = ftCmdID[cmd as keyof typeof ftCmdID];
    assert.ok(cmdInfo, `Command ${cmd} should exist`);
    assert.ok(typeof cmdInfo.cmd === 'number', `Command ${cmd} should have numeric cmd`);
    assert.ok(typeof cmdInfo.name === 'string', `Command ${cmd} should have string name`);
  }
});

test('ftCmdID should have all required quote commands', () => {
  const requiredCommands = ['QotSub', 'QotGetBasicQot', 'QotGetKL', 'QotGetOrderBook'];

  for (const cmd of requiredCommands) {
    const cmdInfo = ftCmdID[cmd as keyof typeof ftCmdID];
    assert.ok(cmdInfo, `Command ${cmd} should exist`);
    assert.ok(typeof cmdInfo.cmd === 'number', `Command ${cmd} should have numeric cmd`);
    assert.ok(typeof cmdInfo.name === 'string', `Command ${cmd} should have string name`);
  }
});
