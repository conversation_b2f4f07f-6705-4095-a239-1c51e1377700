export declare const ftCmdID: {
    readonly InitConnect: {
        readonly cmd: 1001;
        readonly name: "GetGlobalState";
        readonly description: "初始化连接";
    };
    readonly GetGlobalState: {
        readonly cmd: 1002;
        readonly name: "GetGlobalState";
        readonly description: "获取全局状态";
    };
    readonly Notify: {
        readonly cmd: 1003;
        readonly name: "Notify";
        readonly description: "推送通知";
    };
    readonly KeepAlive: {
        readonly cmd: 1004;
        readonly name: "KeepAlive";
        readonly description: "心跳";
    };
    readonly GetUserInfo: {
        readonly cmd: 1005;
        readonly name: "GetUserInfo";
        readonly description: "获取用户信息";
    };
    readonly GetDelayStatistics: {
        readonly cmd: 1007;
        readonly name: "GetDelayStatistics";
        readonly description: "获取延迟统计";
    };
    readonly QotSub: {
        readonly cmd: 3001;
        readonly name: "Qot_Sub";
        readonly description: "订阅或者反订阅";
    };
    readonly QotRegQotPush: {
        readonly cmd: 3002;
        readonly name: "Qot_RegQotPush";
        readonly description: "注册推送";
    };
    readonly QotGetSubInfo: {
        readonly cmd: 3003;
        readonly name: "Qot_GetSubInfo";
        readonly description: "获取订阅信息";
    };
    readonly QotGetBasicQot: {
        readonly cmd: 3004;
        readonly name: "Qot_GetBasicQot";
        readonly description: "获取基本行情";
    };
    readonly QotUpdateBasicQot: {
        readonly cmd: 3005;
        readonly name: "Qot_UpdateBasicQot";
        readonly description: "推送基本行情";
    };
    readonly QotGetKL: {
        readonly cmd: 3006;
        readonly name: "Qot_GetKL";
        readonly description: "获取K线";
    };
    readonly QotUpdateKL: {
        readonly cmd: 3007;
        readonly name: "Qot_UpdateKL";
        readonly description: "推送K线";
    };
    readonly QotGetRT: {
        readonly cmd: 3008;
        readonly name: "Qot_GetRT";
        readonly description: "获取分时";
    };
    readonly QotUpdateRT: {
        readonly cmd: 3009;
        readonly name: "Qot_UpdateRT";
        readonly description: "获取分时";
    };
    readonly QotGetTicker: {
        readonly cmd: 3010;
        readonly name: "Qot_GetTicker";
        readonly description: "获取逐笔";
    };
    readonly QotUpdateTicker: {
        readonly cmd: 3011;
        readonly name: "Qot_UpdateTicker";
        readonly description: "推送逐笔";
    };
    readonly QotGetOrderBook: {
        readonly cmd: 3012;
        readonly name: "Qot_GetOrderBook";
        readonly description: "获取买卖盘";
    };
    readonly QotUpdateOrderBook: {
        readonly cmd: 3013;
        readonly name: "Qot_UpdateOrderBook";
        readonly description: "推送买卖盘";
    };
    readonly QotGetBroker: {
        readonly cmd: 3014;
        readonly name: "Qot_GetBroker";
        readonly description: "获取经纪队列";
    };
    readonly QotUpdateBroker: {
        readonly cmd: 3015;
        readonly name: "Qot_UpdateBroker";
        readonly description: "推送经纪队列";
    };
    readonly QotUpdatePriceReminder: {
        readonly cmd: 3019;
        readonly name: "Qot_UpdatePriceReminder";
        readonly description: "推送到价提醒";
    };
    readonly QotGetHistoryKL: {
        readonly cmd: 3100;
        readonly name: "Qot_GetHistoryKL";
        readonly description: "获取历史K线";
    };
    readonly QotGetHistoryKLPoints: {
        readonly cmd: 3101;
        readonly name: "Qot_GetHistoryKLPoints";
        readonly description: "获取多只股票历史单点K线";
    };
    readonly QotGetRehab: {
        readonly cmd: 3102;
        readonly name: "Qot_GetRehab";
        readonly description: "获取复权信息";
    };
    readonly QotRequestHistoryKL: {
        readonly cmd: 3103;
        readonly name: "Qot_RequestHistoryKL";
        readonly description: "拉取历史K线，不读本地历史数据DB";
    };
    readonly QotRequestHistoryKLQuota: {
        readonly cmd: 3104;
        readonly name: "Qot_RequestHistoryKLQuota";
        readonly description: "拉取历史K线已经用掉的额度";
    };
    readonly QotRequestRehab: {
        readonly cmd: 3105;
        readonly name: "Qot_RequestRehab";
        readonly description: "拉取复权信息，不读本地历史数据DB";
    };
    readonly QotGetTradeDate: {
        readonly cmd: 3200;
        readonly name: "Qot_GetTradeDate";
        readonly description: "获取市场交易日";
    };
    readonly QotGetSuspend: {
        readonly cmd: 3201;
        readonly name: "Qot_GetSuspend";
        readonly description: "获取股票停牌信息";
    };
    readonly QotGetStaticInfo: {
        readonly cmd: 3202;
        readonly name: "Qot_GetStaticInfo";
        readonly description: "获取股票静态信息";
    };
    readonly QotGetSecuritySnapshot: {
        readonly cmd: 3203;
        readonly name: "Qot_GetSecuritySnapshot";
        readonly description: "获取股票快照";
    };
    readonly QotGetPlateSet: {
        readonly cmd: 3204;
        readonly name: "Qot_GetPlateSet";
        readonly description: "获取板块集合下的板块";
    };
    readonly QotGetPlateSecurity: {
        readonly cmd: 3205;
        readonly name: "Qot_GetPlateSecurity";
        readonly description: "获取板块下的股票";
    };
    readonly QotGetReference: {
        readonly cmd: 3206;
        readonly name: "Qot_GetReference";
        readonly description: "获取正股相关股票，暂时只有窝轮";
    };
    readonly QotGetOwnerPlate: {
        readonly cmd: 3207;
        readonly name: "Qot_GetOwnerPlate";
        readonly description: "获取股票所属板块";
    };
    readonly QotGetHoldingChangeList: {
        readonly cmd: 3208;
        readonly name: "Qot_GetHoldingChangeList";
        readonly description: "获取大股东持股变化列表";
    };
    readonly QotGetOptionChain: {
        readonly cmd: 3209;
        readonly name: "Qot_GetOptionChain";
        readonly description: "获取期权链";
    };
    readonly QotGetWarrant: {
        readonly cmd: 3210;
        readonly name: "Qot_GetWarrant";
        readonly description: "获取涡轮";
    };
    readonly QotGetCapitalFlow: {
        readonly cmd: 3211;
        readonly name: "Qot_GetCapitalFlow";
        readonly description: "获取资金流向";
    };
    readonly QotGetCapitalDistribution: {
        readonly cmd: 3212;
        readonly name: "Qot_GetCapitalDistribution";
        readonly description: "获取资金分布";
    };
    readonly QotGetUserSecurity: {
        readonly cmd: 3213;
        readonly name: "Qot_GetUserSecurity";
        readonly description: "获取自选股分组下的股票";
    };
    readonly QotModifyUserSecurity: {
        readonly cmd: 3214;
        readonly name: "Qot_ModifyUserSecurity";
        readonly description: "修改自选股分组下的股票";
    };
    readonly QotStockFilter: {
        readonly cmd: 3215;
        readonly name: "Qot_StockFilter";
        readonly description: "条件选股";
    };
    readonly QotGetCodeChange: {
        readonly cmd: 3216;
        readonly name: "Qot_GetCodeChange";
        readonly description: "获取股票代码变化信息";
    };
    readonly QotGetIpoList: {
        readonly cmd: 3217;
        readonly name: "Qot_GetIpoList";
        readonly description: "获取新股IPO";
    };
    readonly QotGetFutureInfo: {
        readonly cmd: 3218;
        readonly name: "Qot_GetFutureInfo";
        readonly description: "获取期货合约资料";
    };
    readonly QotRequestTradeDate: {
        readonly cmd: 3219;
        readonly name: "Qot_RequestTradeDate";
        readonly description: "获取市场交易日";
    };
    readonly QotSetPriceReminder: {
        readonly cmd: 3220;
        readonly name: "Qot_SetPriceReminder";
        readonly description: "设置到价提醒";
    };
    readonly QotGetPriceReminder: {
        readonly cmd: 3221;
        readonly name: "Qot_GetPriceReminder";
        readonly description: "获取到价提醒";
    };
    readonly QotGetUserSecurityGroup: {
        readonly cmd: 3222;
        readonly name: "Qot_GetUserSecurityGroup";
        readonly description: "获取自选股分组列表";
    };
    readonly QotGetMarketState: {
        readonly cmd: 3223;
        readonly name: "Qot_GetMarketState";
        readonly description: "获取股票对应市场状态";
    };
    readonly QotGetOptionExpirationDate: {
        readonly cmd: 3224;
        readonly name: "Qot_GetOptionExpirationDate";
        readonly description: "获取期权链到期日";
    };
    readonly TrdGetAccList: {
        readonly cmd: 2001;
        readonly name: "Trd_GetAccList";
        readonly description: "获取交易账户列表";
    };
    readonly TrdUnlockTrade: {
        readonly cmd: 2005;
        readonly name: "Trd_UnlockTrade";
        readonly description: "解锁或锁定交易";
    };
    readonly TrdSubAccPush: {
        readonly cmd: 2008;
        readonly name: "Trd_SubAccPush";
        readonly description: "订阅接收推送数据的交易账户";
    };
    readonly TrdGetFunds: {
        readonly cmd: 2101;
        readonly name: "Trd_GetFunds";
        readonly description: "获取账户资金";
    };
    readonly TrdGetPositionList: {
        readonly cmd: 2102;
        readonly name: "Trd_GetPositionList";
        readonly description: "获取账户持仓";
    };
    readonly TrdGetMaxTrdQtys: {
        readonly cmd: 2111;
        readonly name: "Trd_GetMaxTrdQtys";
        readonly description: "获取最大交易数量";
    };
    readonly TrdGetOrderList: {
        readonly cmd: 2201;
        readonly name: "Trd_GetOrderList";
        readonly description: "获取订单列表";
    };
    readonly TrdPlaceOrder: {
        readonly cmd: 2202;
        readonly name: "Trd_PlaceOrder";
        readonly description: "下单";
    };
    readonly TrdModifyOrder: {
        readonly cmd: 2205;
        readonly name: "Trd_ModifyOrder";
        readonly description: "修改订单";
    };
    readonly TrdUpdateOrder: {
        readonly cmd: 2208;
        readonly name: "Trd_UpdateOrder";
        readonly description: "订单状态变动通知(推送)";
    };
    readonly TrdGetOrderFillList: {
        readonly cmd: 2211;
        readonly name: "Trd_GetOrderFillList";
        readonly description: "获取成交列表";
    };
    readonly TrdUpdateOrderFill: {
        readonly cmd: 2218;
        readonly name: "Trd_UpdateOrderFill";
        readonly description: "成交通知(推送)";
    };
    readonly TrdGetHistoryOrderList: {
        readonly cmd: 2221;
        readonly name: "Trd_GetHistoryOrderList";
        readonly description: "获取历史订单列表";
    };
    readonly TrdGetHistoryOrderFillList: {
        readonly cmd: 2222;
        readonly name: "Trd_GetHistoryOrderFillList";
        readonly description: "获取历史成交列表";
    };
    readonly TrdGetMarginRatio: {
        readonly cmd: 2223;
        readonly name: "Trd_GetMarginRatio";
        readonly description: "获取融资融券数据";
    };
    readonly TrdGetOrderFee: {
        readonly cmd: 2225;
        readonly name: "Trd_GetOrderFee";
        readonly description: "获取订单收费明细数据";
    };
    readonly TrdFlowSummary: {
        readonly cmd: 2226;
        readonly name: "Trd_FlowSummary";
        readonly description: "获取资金流水";
    };
};
export declare type FtCmdID = typeof ftCmdID;
//# sourceMappingURL=constants.d.ts.map