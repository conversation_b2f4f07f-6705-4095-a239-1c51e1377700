export interface ProtoMessage {
    encode(message: any): {
        finish(): Uint8Array;
    };
    decode(buffer: Uint8Array): any;
    create(properties?: any): any;
}
export interface ProtoRoot {
    lookup(path: string): ProtoMessage;
}
export interface CommonSecurity {
    market: number;
    code: string;
}
export interface CommonPacketID {
    connID: number;
    serialNo: number;
}
export interface CommonTrdHeader {
    trdEnv: number;
    accID: number;
    trdMarket: number;
}
export declare enum RetType {
    RetType_Succeed = 0,
    RetType_Failed = -1,
    RetType_TimeOut = -100,
    RetType_DisConnect = -200,
    RetType_Unknown = -400,
    RetType_Invalid = -500
}
export declare enum Market {
    Market_Unknown = 0,
    Market_HK_Security = 1,
    Market_HK_Future = 2,
    Market_US_Security = 11,
    Market_CNSH_Security = 21,
    Market_CNSZ_Security = 22,
    Market_SG_Security = 31,
    Market_JP_Security = 41
}
export declare enum TrdSide {
    TrdSide_Unknown = 0,
    TrdSide_Buy = 1,
    TrdSide_Sell = 2,
    TrdSide_SellShort = 3,
    TrdSide_BuyBack = 4
}
export declare enum OrderType {
    OrderType_Unknown = 0,
    OrderType_Normal = 1,
    OrderType_Market = 2,
    OrderType_AbsoluteLimit = 5,
    OrderType_Auction = 6,
    OrderType_AuctionLimit = 7,
    OrderType_SpecialLimit = 8,
    OrderType_SpecialLimit_All = 9,
    OrderType_Stop = 10,
    OrderType_StopLimit = 11,
    OrderType_MarketifTouched = 12,
    OrderType_LimitifTouched = 13,
    OrderType_TrailingStop = 14,
    OrderType_TrailingStopLimit = 15
}
export declare enum OrderStatus {
    OrderStatus_Unknown = 0,
    OrderStatus_Unsubmitted = 1,
    OrderStatus_WaitingSubmit = 2,
    OrderStatus_Submitting = 3,
    OrderStatus_SubmitFailed = 4,
    OrderStatus_TimeOut = 5,
    OrderStatus_Submitted = 6,
    OrderStatus_Filled_Part = 10,
    OrderStatus_Filled_All = 11,
    OrderStatus_Cancelling_Part = 12,
    OrderStatus_Cancelling_All = 13,
    OrderStatus_Cancelled_Part = 14,
    OrderStatus_Cancelled_All = 15,
    OrderStatus_Failed = 21,
    OrderStatus_Disabled = 22,
    OrderStatus_Deleted = 23
}
export declare enum KLType {
    KLType_Unknown = 0,
    KLType_1Min = 1,
    KLType_3Min = 2,
    KLType_5Min = 3,
    KLType_15Min = 4,
    KLType_30Min = 5,
    KLType_60Min = 6,
    KLType_Day = 7,
    KLType_Week = 8,
    KLType_Month = 9,
    KLType_Quarter = 10,
    KLType_Year = 11
}
export declare enum SubType {
    SubType_Unknown = 0,
    SubType_Basic = 1,
    SubType_OrderBook = 2,
    SubType_Ticker = 4,
    SubType_RT = 5,
    SubType_KL_Day = 6,
    SubType_KL_5Min = 7,
    SubType_KL_15Min = 8,
    SubType_KL_30Min = 9,
    SubType_KL_60Min = 10,
    SubType_KL_1Min = 11,
    SubType_KL_Week = 12,
    SubType_KL_Month = 13,
    SubType_Broker = 14
}
//# sourceMappingURL=proto.d.ts.map