// 基础类型定义
// 错误枚举
export var FtWebsocketError;
(function (FtWebsocketError) {
    FtWebsocketError[FtWebsocketError["WEBSOCKET_ERROR_TIMEOUT"] = 1] = "WEBSOCKET_ERROR_TIMEOUT";
    FtWebsocketError[FtWebsocketError["WEBSOCKET_ERROR_OPEND_TIMEOUT"] = 2] = "WEBSOCKET_ERROR_OPEND_TIMEOUT";
    FtWebsocketError[FtWebsocketError["WEBSOCKET_ERROR_DISCONNECT"] = 3] = "WEBSOCKET_ERROR_DISCONNECT";
})(FtWebsocketError || (FtWebsocketError = {}));
export var FtWebsocketRecvError;
(function (FtWebsocketRecvError) {
    FtWebsocketRecvError[FtWebsocketRecvError["ErrorSign"] = 1] = "ErrorSign";
})(FtWebsocketRecvError || (FtWebsocketRecvError = {}));
export var FtApiCmdID;
(function (FtApiCmdID) {
    FtApiCmdID[FtApiCmdID["Init"] = 1] = "Init";
    FtApiCmdID[FtApiCmdID["OpenDisConnect"] = 2] = "OpenDisConnect";
})(FtApiCmdID || (FtApiCmdID = {}));
//# sourceMappingURL=base.js.map