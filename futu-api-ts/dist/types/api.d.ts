export interface CmdInfo {
    cmd: number;
    name: string;
    description: string;
}
export interface BaseRequest {
    [key: string]: any;
}
export interface BaseResponse {
    retType: number;
    retMsg?: string;
    errCode?: number;
    [key: string]: any;
}
export interface TrdAccInfo {
    trdEnv: number;
    accID: number;
    accType: number;
}
export interface Security {
    market: number;
    code: string;
}
export interface QotSecurity extends Security {
}
export interface BasicQot {
    security: QotSecurity;
    isSuspended: boolean;
    listTime: string;
    priceSpread: number;
    updateTime: string;
    highPrice: number;
    openPrice: number;
    lowPrice: number;
    curPrice: number;
    lastClosePrice: number;
    volume: number;
    turnover: number;
    turnoverRate: number;
    amplitude: number;
}
export interface KLPoint {
    time: string;
    isBlank: boolean;
    highPrice: number;
    openPrice: number;
    lowPrice: number;
    closePrice: number;
    lastClosePrice: number;
    volume: number;
    turnover: number;
    pe: number;
    pb: number;
    changeRate: number;
}
export interface Order {
    trdSide: number;
    orderType: number;
    orderStatus: number;
    orderID: number;
    orderIDEx: string;
    price: number;
    qty: number;
    fillQty: number;
    fillAvgPrice: number;
    createTime: string;
    updateTime: string;
    security: Security;
}
export interface Position {
    positionSide: number;
    canSellQty: number;
    qty: number;
    costPrice: number;
    val: number;
    plVal: number;
    plRatio: number;
    security: Security;
}
export interface Funds {
    power: number;
    totalAssets: number;
    cash: number;
    marketVal: number;
    frozenCash: number;
    debtCash: number;
    avlWithdrawalCash: number;
}
export interface GetAccListRequest extends BaseRequest {
    c2s?: {
        userID?: number;
    };
}
export interface GetAccListResponse extends BaseResponse {
    s2c?: {
        accList?: TrdAccInfo[];
    };
}
export interface UnlockTradeRequest extends BaseRequest {
    c2s: {
        unlock: boolean;
        pwdMD5: string;
        securityFirm?: number;
    };
}
export interface GetFundsRequest extends BaseRequest {
    c2s: {
        header: {
            trdEnv: number;
            accID: number;
            trdMarket: number;
        };
        refreshCache?: boolean;
        currency?: number;
    };
}
export interface GetFundsResponse extends BaseResponse {
    s2c?: {
        header?: any;
        funds?: Funds;
    };
}
export interface GetPositionListRequest extends BaseRequest {
    c2s: {
        header: {
            trdEnv: number;
            accID: number;
            trdMarket: number;
        };
        filterConditions?: any;
        filterPLRatioMin?: number;
        filterPLRatioMax?: number;
        refreshCache?: boolean;
    };
}
export interface GetPositionListResponse extends BaseResponse {
    s2c?: {
        header?: any;
        positionList?: Position[];
    };
}
export interface PlaceOrderRequest extends BaseRequest {
    c2s: {
        packetID: {
            connID: number;
            serialNo: number;
        };
        header: {
            trdEnv: number;
            accID: number;
            trdMarket: number;
        };
        trdSide: number;
        orderType: number;
        code: string;
        qty: number;
        price?: number;
        adjustPrice?: boolean;
        adjustSideAndLimit?: number;
        secMarket?: number;
        remark?: string;
    };
}
export interface PlaceOrderResponse extends BaseResponse {
    s2c?: {
        header?: any;
        orderID?: number;
    };
}
//# sourceMappingURL=api.d.ts.map