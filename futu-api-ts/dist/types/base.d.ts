/// <reference types="node" />
export interface WebSocketConfig {
    ip: string;
    port: number;
    ssl: boolean;
}
export interface WebSocketState {
    closing: boolean;
    login: boolean;
}
export interface PacketHeader {
    sign: string;
    cmd: number;
    section: number;
    error: number;
    errmsg: string;
    buff?: ArrayBuffer;
}
export interface PromisePoolItem {
    section: number;
    resolve: (value: any) => void;
    reject: (reason?: any) => void;
    socket: WebSocket;
    timer?: NodeJS.Timeout | undefined;
}
export interface ResponseData {
    cmd: number;
    buff?: ArrayBuffer;
    error: number;
    errmsg: string;
}
export interface InitWebSocketRequest {
    c2s: {
        websocketKey: string;
        programmingLanguage: string;
    };
}
export interface InitWebSocketResponse {
    s2c: {
        connID: number;
    };
}
export declare enum FtWebsocketError {
    WEBSOCKET_ERROR_TIMEOUT = 1,
    WEBSOCKET_ERROR_OPEND_TIMEOUT = 2,
    WEBSOCKET_ERROR_DISCONNECT = 3
}
export declare enum FtWebsocketRecvError {
    ErrorSign = 1
}
export declare enum FtApiCmdID {
    Init = 1,
    OpenDisConnect = 2
}
export declare type LoginCallback = (success: boolean, message: any) => void;
export declare type PushCallback = (cmd: number, data: any) => void;
export declare type MessageCallback = (message: any) => void;
export declare type ErrorCallback = (error: any) => void;
export declare type CloseCallback = (event: any) => void;
//# sourceMappingURL=base.d.ts.map