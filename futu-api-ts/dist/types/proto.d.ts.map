{"version": 3, "file": "proto.d.ts", "sourceRoot": "", "sources": ["../../src/types/proto.ts"], "names": [], "mappings": "AAEA,MAAM,WAAW,YAAY;IAC3B,MAAM,CAAC,OAAO,EAAE,GAAG,GAAG;QAAE,MAAM,IAAI,UAAU,CAAA;KAAE,CAAC;IAC/C,MAAM,CAAC,MAAM,EAAE,UAAU,GAAG,GAAG,CAAC;IAChC,MAAM,CAAC,UAAU,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CAC/B;AAED,MAAM,WAAW,SAAS;IACxB,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,YAAY,CAAC;CACpC;AAGD,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,eAAe;IAC9B,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;CACnB;AAGD,oBAAY,OAAO;IACjB,eAAe,IAAI;IACnB,cAAc,KAAK;IACnB,eAAe,OAAO;IACtB,kBAAkB,OAAO;IACzB,eAAe,OAAO;IACtB,eAAe,OAAO;CACvB;AAGD,oBAAY,MAAM;IAChB,cAAc,IAAI;IAClB,kBAAkB,IAAI;IACtB,gBAAgB,IAAI;IACpB,kBAAkB,KAAK;IACvB,oBAAoB,KAAK;IACzB,oBAAoB,KAAK;IACzB,kBAAkB,KAAK;IACvB,kBAAkB,KAAK;CACxB;AAGD,oBAAY,OAAO;IACjB,eAAe,IAAI;IACnB,WAAW,IAAI;IACf,YAAY,IAAI;IAChB,iBAAiB,IAAI;IACrB,eAAe,IAAI;CACpB;AAGD,oBAAY,SAAS;IACnB,iBAAiB,IAAI;IACrB,gBAAgB,IAAI;IACpB,gBAAgB,IAAI;IACpB,uBAAuB,IAAI;IAC3B,iBAAiB,IAAI;IACrB,sBAAsB,IAAI;IAC1B,sBAAsB,IAAI;IAC1B,0BAA0B,IAAI;IAC9B,cAAc,KAAK;IACnB,mBAAmB,KAAK;IACxB,yBAAyB,KAAK;IAC9B,wBAAwB,KAAK;IAC7B,sBAAsB,KAAK;IAC3B,2BAA2B,KAAK;CACjC;AAGD,oBAAY,WAAW;IACrB,mBAAmB,IAAI;IACvB,uBAAuB,IAAI;IAC3B,yBAAyB,IAAI;IAC7B,sBAAsB,IAAI;IAC1B,wBAAwB,IAAI;IAC5B,mBAAmB,IAAI;IACvB,qBAAqB,IAAI;IACzB,uBAAuB,KAAK;IAC5B,sBAAsB,KAAK;IAC3B,2BAA2B,KAAK;IAChC,0BAA0B,KAAK;IAC/B,0BAA0B,KAAK;IAC/B,yBAAyB,KAAK;IAC9B,kBAAkB,KAAK;IACvB,oBAAoB,KAAK;IACzB,mBAAmB,KAAK;CACzB;AAGD,oBAAY,MAAM;IAChB,cAAc,IAAI;IAClB,WAAW,IAAI;IACf,WAAW,IAAI;IACf,WAAW,IAAI;IACf,YAAY,IAAI;IAChB,YAAY,IAAI;IAChB,YAAY,IAAI;IAChB,UAAU,IAAI;IACd,WAAW,IAAI;IACf,YAAY,IAAI;IAChB,cAAc,KAAK;IACnB,WAAW,KAAK;CACjB;AAGD,oBAAY,OAAO;IACjB,eAAe,IAAI;IACnB,aAAa,IAAI;IACjB,iBAAiB,IAAI;IACrB,cAAc,IAAI;IAClB,UAAU,IAAI;IACd,cAAc,IAAI;IAClB,eAAe,IAAI;IACnB,gBAAgB,IAAI;IACpB,gBAAgB,IAAI;IACpB,gBAAgB,KAAK;IACrB,eAAe,KAAK;IACpB,eAAe,KAAK;IACpB,gBAAgB,KAAK;IACrB,cAAc,KAAK;CACpB"}