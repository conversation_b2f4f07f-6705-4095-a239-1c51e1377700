// Protocol Buffers 相关类型定义
// 返回类型枚举
export var RetType;
(function (RetType) {
    RetType[RetType["RetType_Succeed"] = 0] = "RetType_Succeed";
    RetType[RetType["RetType_Failed"] = -1] = "RetType_Failed";
    RetType[RetType["RetType_TimeOut"] = -100] = "RetType_TimeOut";
    RetType[RetType["RetType_DisConnect"] = -200] = "RetType_DisConnect";
    RetType[RetType["RetType_Unknown"] = -400] = "RetType_Unknown";
    RetType[RetType["RetType_Invalid"] = -500] = "RetType_Invalid";
})(RetType || (RetType = {}));
// 市场类型
export var Market;
(function (Market) {
    Market[Market["Market_Unknown"] = 0] = "Market_Unknown";
    Market[Market["Market_HK_Security"] = 1] = "Market_HK_Security";
    Market[Market["Market_HK_Future"] = 2] = "Market_HK_Future";
    Market[Market["Market_US_Security"] = 11] = "Market_US_Security";
    Market[Market["Market_CNSH_Security"] = 21] = "Market_CNSH_Security";
    Market[Market["Market_CNSZ_Security"] = 22] = "Market_CNSZ_Security";
    Market[Market["Market_SG_Security"] = 31] = "Market_SG_Security";
    Market[Market["Market_JP_Security"] = 41] = "Market_JP_Security";
})(Market || (Market = {}));
// 交易方向
export var TrdSide;
(function (TrdSide) {
    TrdSide[TrdSide["TrdSide_Unknown"] = 0] = "TrdSide_Unknown";
    TrdSide[TrdSide["TrdSide_Buy"] = 1] = "TrdSide_Buy";
    TrdSide[TrdSide["TrdSide_Sell"] = 2] = "TrdSide_Sell";
    TrdSide[TrdSide["TrdSide_SellShort"] = 3] = "TrdSide_SellShort";
    TrdSide[TrdSide["TrdSide_BuyBack"] = 4] = "TrdSide_BuyBack";
})(TrdSide || (TrdSide = {}));
// 订单类型
export var OrderType;
(function (OrderType) {
    OrderType[OrderType["OrderType_Unknown"] = 0] = "OrderType_Unknown";
    OrderType[OrderType["OrderType_Normal"] = 1] = "OrderType_Normal";
    OrderType[OrderType["OrderType_Market"] = 2] = "OrderType_Market";
    OrderType[OrderType["OrderType_AbsoluteLimit"] = 5] = "OrderType_AbsoluteLimit";
    OrderType[OrderType["OrderType_Auction"] = 6] = "OrderType_Auction";
    OrderType[OrderType["OrderType_AuctionLimit"] = 7] = "OrderType_AuctionLimit";
    OrderType[OrderType["OrderType_SpecialLimit"] = 8] = "OrderType_SpecialLimit";
    OrderType[OrderType["OrderType_SpecialLimit_All"] = 9] = "OrderType_SpecialLimit_All";
    OrderType[OrderType["OrderType_Stop"] = 10] = "OrderType_Stop";
    OrderType[OrderType["OrderType_StopLimit"] = 11] = "OrderType_StopLimit";
    OrderType[OrderType["OrderType_MarketifTouched"] = 12] = "OrderType_MarketifTouched";
    OrderType[OrderType["OrderType_LimitifTouched"] = 13] = "OrderType_LimitifTouched";
    OrderType[OrderType["OrderType_TrailingStop"] = 14] = "OrderType_TrailingStop";
    OrderType[OrderType["OrderType_TrailingStopLimit"] = 15] = "OrderType_TrailingStopLimit";
})(OrderType || (OrderType = {}));
// 订单状态
export var OrderStatus;
(function (OrderStatus) {
    OrderStatus[OrderStatus["OrderStatus_Unknown"] = 0] = "OrderStatus_Unknown";
    OrderStatus[OrderStatus["OrderStatus_Unsubmitted"] = 1] = "OrderStatus_Unsubmitted";
    OrderStatus[OrderStatus["OrderStatus_WaitingSubmit"] = 2] = "OrderStatus_WaitingSubmit";
    OrderStatus[OrderStatus["OrderStatus_Submitting"] = 3] = "OrderStatus_Submitting";
    OrderStatus[OrderStatus["OrderStatus_SubmitFailed"] = 4] = "OrderStatus_SubmitFailed";
    OrderStatus[OrderStatus["OrderStatus_TimeOut"] = 5] = "OrderStatus_TimeOut";
    OrderStatus[OrderStatus["OrderStatus_Submitted"] = 6] = "OrderStatus_Submitted";
    OrderStatus[OrderStatus["OrderStatus_Filled_Part"] = 10] = "OrderStatus_Filled_Part";
    OrderStatus[OrderStatus["OrderStatus_Filled_All"] = 11] = "OrderStatus_Filled_All";
    OrderStatus[OrderStatus["OrderStatus_Cancelling_Part"] = 12] = "OrderStatus_Cancelling_Part";
    OrderStatus[OrderStatus["OrderStatus_Cancelling_All"] = 13] = "OrderStatus_Cancelling_All";
    OrderStatus[OrderStatus["OrderStatus_Cancelled_Part"] = 14] = "OrderStatus_Cancelled_Part";
    OrderStatus[OrderStatus["OrderStatus_Cancelled_All"] = 15] = "OrderStatus_Cancelled_All";
    OrderStatus[OrderStatus["OrderStatus_Failed"] = 21] = "OrderStatus_Failed";
    OrderStatus[OrderStatus["OrderStatus_Disabled"] = 22] = "OrderStatus_Disabled";
    OrderStatus[OrderStatus["OrderStatus_Deleted"] = 23] = "OrderStatus_Deleted";
})(OrderStatus || (OrderStatus = {}));
// K线类型
export var KLType;
(function (KLType) {
    KLType[KLType["KLType_Unknown"] = 0] = "KLType_Unknown";
    KLType[KLType["KLType_1Min"] = 1] = "KLType_1Min";
    KLType[KLType["KLType_3Min"] = 2] = "KLType_3Min";
    KLType[KLType["KLType_5Min"] = 3] = "KLType_5Min";
    KLType[KLType["KLType_15Min"] = 4] = "KLType_15Min";
    KLType[KLType["KLType_30Min"] = 5] = "KLType_30Min";
    KLType[KLType["KLType_60Min"] = 6] = "KLType_60Min";
    KLType[KLType["KLType_Day"] = 7] = "KLType_Day";
    KLType[KLType["KLType_Week"] = 8] = "KLType_Week";
    KLType[KLType["KLType_Month"] = 9] = "KLType_Month";
    KLType[KLType["KLType_Quarter"] = 10] = "KLType_Quarter";
    KLType[KLType["KLType_Year"] = 11] = "KLType_Year";
})(KLType || (KLType = {}));
// 订阅类型
export var SubType;
(function (SubType) {
    SubType[SubType["SubType_Unknown"] = 0] = "SubType_Unknown";
    SubType[SubType["SubType_Basic"] = 1] = "SubType_Basic";
    SubType[SubType["SubType_OrderBook"] = 2] = "SubType_OrderBook";
    SubType[SubType["SubType_Ticker"] = 4] = "SubType_Ticker";
    SubType[SubType["SubType_RT"] = 5] = "SubType_RT";
    SubType[SubType["SubType_KL_Day"] = 6] = "SubType_KL_Day";
    SubType[SubType["SubType_KL_5Min"] = 7] = "SubType_KL_5Min";
    SubType[SubType["SubType_KL_15Min"] = 8] = "SubType_KL_15Min";
    SubType[SubType["SubType_KL_30Min"] = 9] = "SubType_KL_30Min";
    SubType[SubType["SubType_KL_60Min"] = 10] = "SubType_KL_60Min";
    SubType[SubType["SubType_KL_1Min"] = 11] = "SubType_KL_1Min";
    SubType[SubType["SubType_KL_Week"] = 12] = "SubType_KL_Week";
    SubType[SubType["SubType_KL_Month"] = 13] = "SubType_KL_Month";
    SubType[SubType["SubType_Broker"] = 14] = "SubType_Broker";
})(SubType || (SubType = {}));
//# sourceMappingURL=proto.js.map