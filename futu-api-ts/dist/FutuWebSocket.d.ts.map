{"version": 3, "file": "FutuWebSocket.d.ts", "sourceRoot": "", "sources": ["../src/FutuWebSocket.ts"], "names": [], "mappings": "AAMA,OAAO,KAAK,EACV,WAAW,EACX,YAAY,EACZ,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EAClB,eAAe,EACf,gBAAgB,EAChB,sBAAsB,EACtB,uBAAuB,EACvB,iBAAiB,EACjB,kBAAkB,EACnB,MAAM,gBAAgB,CAAC;AACxB,OAAO,KAAK,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAMnE,MAAM,CAAC,OAAO,OAAO,aAAa;IAChC,OAAO,CAAC,OAAO,CAAkC;IAC1C,OAAO,EAAE,aAAa,GAAG,IAAI,CAAQ;IACrC,MAAM,EAAE,YAAY,GAAG,IAAI,CAAQ;IAE1C,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,GAAG;QAAE,GAAG,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,MAAM,CAAC;QAAC,WAAW,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI;IAUzF,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI;IAkCjE,SAAS,IAAI,MAAM;IAInB;;;OAGG;IACH,GAAG,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAI5C;;;OAGG;IACH,UAAU,CAAC,GAAG,EAAE,iBAAiB,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAI/D;;;OAGG;IACH,WAAW,CAAC,GAAG,EAAE,kBAAkB,GAAG,OAAO,CAAC,YAAY,CAAC;IAI3D;;;OAGG;IACH,UAAU,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAInD;;;OAGG;IACH,QAAQ,CAAC,GAAG,EAAE,eAAe,GAAG,OAAO,CAAC,gBAAgB,CAAC;IAIzD;;;OAGG;IACH,eAAe,CAAC,GAAG,EAAE,sBAAsB,GAAG,OAAO,CAAC,uBAAuB,CAAC;IAI9E;;;OAGG;IACH,aAAa,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAItD;;;OAGG;IACH,YAAY,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIrD;;;OAGG;IACH,UAAU,CAAC,GAAG,EAAE,iBAAiB,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAI/D;;;OAGG;IACH,WAAW,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIpD;;;OAGG;IACH,gBAAgB,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIzD;;;OAGG;IACH,mBAAmB,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAQ5D;;;OAGG;IACH,uBAAuB,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAQhE;;;OAGG;IACH,cAAc,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIvD;;;OAGG;IACH,WAAW,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIpD;;;OAGG;IACH,cAAc,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIvD;;;OAGG;IACH,cAAc,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIvD;;;OAGG;IACH,UAAU,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAInD;;;OAGG;IACH,UAAU,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAInD;;;OAGG;IACH,SAAS,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIlD;;;OAGG;IACH,WAAW,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIpD;;;OAGG;IACH,YAAY,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIrD;;;OAGG;IACH,KAAK,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAI9C;;;OAGG;IACH,KAAK,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAI9C;;;OAGG;IACH,SAAS,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAMlD;;;OAGG;IACH,YAAY,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIrD;;;OAGG;IACH,gBAAgB,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIzD;;;OAGG;IACH,qBAAqB,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAQ9D;;;OAGG;IACH,YAAY,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAIrD;;;OAGG;IACH,aAAa,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAItD;;;OAGG;IACH,mBAAmB,CAAC,GAAG,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAS5D;;OAEG;IACH,OAAO,CAAC,QAAQ;IAwChB,IAAI,IAAI,IAAI;IAMZ,OAAO,CAAC,OAAO;CAehB"}