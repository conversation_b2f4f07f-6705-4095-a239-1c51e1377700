import FutuWebSocketBase from './FutuWebSocketBase.js';
import { ftCmdID } from './constants.js';
import protobuf from 'protobufjs';
import { createHash } from 'crypto';
import Long from 'long';
import protoRoot from './proto.js';
// 配置 protobuf
protobuf.util.Long = Long;
protobuf.configure();
export default class FutuWebSocket {
    websock = null;
    onlogin = null;
    onPush = null;
    static findCmdObj(cmd) {
        for (const key in ftCmdID) {
            const cmdInfo = ftCmdID[key];
            if (cmdInfo?.cmd === cmd) {
                return cmdInfo;
            }
        }
        return null;
    }
    start(ip, port, ssl, key) {
        if (this.websock == null || this.websock == undefined) {
            this.websock = new FutuWebSocketBase();
            this.websock.regPushCallback(this, this._onPush.bind(this));
        }
        else {
            this.websock.close();
        }
        this.websock.setWsConfig(ip, port, ssl);
        let message = null;
        let keyMD5 = '';
        if (key) {
            keyMD5 = createHash('md5').update(key).digest('hex');
        }
        const InitWebSocketPBMessageRequest = protoRoot.lookup('InitWebSocket.Request');
        const initWebSocketReq = {
            c2s: {
                websocketKey: keyMD5,
                programmingLanguage: 'TypeScript',
            },
        };
        message = InitWebSocketPBMessageRequest.encode(InitWebSocketPBMessageRequest.create(initWebSocketReq)).finish();
        this.websock.initWebSocket(message || undefined);
        this.websock.onlogin = (ret, msg) => {
            if (this.onlogin && typeof this.onlogin === 'function') {
                this.onlogin(ret, msg);
            }
        };
    }
    getConnID() {
        return this.websock?.connID || 0;
    }
    /**
     * 订阅，反订阅
     * @param req 请求包，具体字段请参考Qot_Sub.proto协议
     */
    Sub(req) {
        return this._sendCmd(ftCmdID.QotSub.cmd, req, ftCmdID.QotSub.name);
    }
    /**
     * 获取交易帐号列表
     * @param req 请求包，具体字段请参考Trd_GetAccList.proto协议
     */
    GetAccList(req) {
        return this._sendCmd(ftCmdID.TrdGetAccList.cmd, req, ftCmdID.TrdGetAccList.name);
    }
    /**
     * 解锁，针对OpenD解锁一次即可
     * @param req 请求包，具体字段请参考Trd_UnlockTrade.proto协议
     */
    UnlockTrade(req) {
        return this._sendCmd(ftCmdID.TrdUnlockTrade.cmd, req, ftCmdID.TrdUnlockTrade.name);
    }
    /**
     * 订阅接收推送数据的交易账户
     * @param req 请求包，具体字段请参考Trd_SubAccPush.proto协议
     */
    SubAccPush(req) {
        return this._sendCmd(ftCmdID.TrdSubAccPush.cmd, req, ftCmdID.TrdSubAccPush.name);
    }
    /**
     * 获取账户资金
     * @param req 请求包，具体字段请参考Trd_GetFunds.proto协议
     */
    GetFunds(req) {
        return this._sendCmd(ftCmdID.TrdGetFunds.cmd, req, ftCmdID.TrdGetFunds.name);
    }
    /**
     * 获取账户持仓
     * @param req 请求包，具体字段请参考Trd_GetPositionList.proto协议
     */
    GetPositionList(req) {
        return this._sendCmd(ftCmdID.TrdGetPositionList.cmd, req, ftCmdID.TrdGetPositionList.name);
    }
    /**
     * 获取最大交易数量
     * @param req 请求包，具体字段请参考Trd_GetMaxTrdQtys.proto协议
     */
    GetMaxTrdQtys(req) {
        return this._sendCmd(ftCmdID.TrdGetMaxTrdQtys.cmd, req, ftCmdID.TrdGetMaxTrdQtys.name);
    }
    /**
     * 获取当日订单列表
     * @param req 请求包，具体字段请参考Trd_GetOrderList.proto协议
     */
    GetOrderList(req) {
        return this._sendCmd(ftCmdID.TrdGetOrderList.cmd, req, ftCmdID.TrdGetOrderList.name);
    }
    /**
     * 下单
     * @param req 请求包，具体字段请参考Trd_PlaceOrder.proto协议，PacketID不需填写，发送时接口会填
     */
    PlaceOrder(req) {
        return this._sendCmd(ftCmdID.TrdPlaceOrder.cmd, req, ftCmdID.TrdPlaceOrder.name);
    }
    /**
     * 修改订单
     * @param req 请求包，具体字段请参考Trd_ModifyOrder.proto协议，PacketID不需填写，发送时接口会填
     */
    ModifyOrder(req) {
        return this._sendCmd(ftCmdID.TrdModifyOrder.cmd, req, ftCmdID.TrdModifyOrder.name);
    }
    /**
     * 获取当日成交列表
     * @param req 请求包，具体字段请参考Trd_GetOrderFillList.proto协议
     */
    GetOrderFillList(req) {
        return this._sendCmd(ftCmdID.TrdGetOrderFillList.cmd, req, ftCmdID.TrdGetOrderFillList.name);
    }
    /**
     * 获取历史订单列表
     * @param req 请求包，具体字段请参考Trd_GetHistoryOrderList.proto协议
     */
    GetHistoryOrderList(req) {
        return this._sendCmd(ftCmdID.TrdGetHistoryOrderList.cmd, req, ftCmdID.TrdGetHistoryOrderList.name);
    }
    /**
     * 获取历史成交列表
     * @param req 请求包，具体字段请参考Trd_GetHistoryOrderFillList.proto协议
     */
    GetHistoryOrderFillList(req) {
        return this._sendCmd(ftCmdID.TrdGetHistoryOrderFillList.cmd, req, ftCmdID.TrdGetHistoryOrderFillList.name);
    }
    /**
     * 获取融资融券数据
     * @param req 请求包，具体字段请参考Trd_GetMarginRatio.proto协议
     */
    GetMarginRatio(req) {
        return this._sendCmd(ftCmdID.TrdGetMarginRatio.cmd, req, ftCmdID.TrdGetMarginRatio.name);
    }
    /**
     * 获取订单收费明细数据
     * @param req 请求包，具体字段请参考Trd_GetOrderFee.proto协议
     */
    GetOrderFee(req) {
        return this._sendCmd(ftCmdID.TrdGetOrderFee.cmd, req, ftCmdID.TrdGetOrderFee.name);
    }
    /**
     * 获取资金流水
     * @param req 请求包，具体字段请参考Trd_FlowSummary.proto协议
     */
    GetFlowSummary(req) {
        return this._sendCmd(ftCmdID.TrdFlowSummary.cmd, req, ftCmdID.TrdFlowSummary.name);
    }
    /**
     * 请求全局状态
     * @param req 具体字段请参考GetGlobalState.proto协议
     */
    GetGlobalState(req) {
        return this._sendCmd(ftCmdID.GetGlobalState.cmd, req, ftCmdID.GetGlobalState.name);
    }
    /**
     * 注册推送
     * @param req 具体字段请参考Qot_RegQotPush.proto协议
     */
    RegQotPush(req) {
        return this._sendCmd(ftCmdID.QotRegQotPush.cmd, req, ftCmdID.QotRegQotPush.name);
    }
    /**
     * 获取订阅信息
     * @param req 具体字段请参考Qot_GetSubInfo.proto协议
     */
    GetSubInfo(req) {
        return this._sendCmd(ftCmdID.QotGetSubInfo.cmd, req, ftCmdID.QotGetSubInfo.name);
    }
    /**
     * 获取逐笔
     * @param req 具体字段请参考Qot_GetTicker.proto协议
     */
    GetTicker(req) {
        return this._sendCmd(ftCmdID.QotGetTicker.cmd, req, ftCmdID.QotGetTicker.name);
    }
    /**
     * 获取报价
     * @param req 具体字段请参考Qot_GetBasicQot.proto协议
     */
    GetBasicQot(req) {
        return this._sendCmd(ftCmdID.QotGetBasicQot.cmd, req, ftCmdID.QotGetBasicQot.name);
    }
    /**
     * 获取摆盘
     * @param req 具体字段请参考Qot_GetOrderBook.proto协议
     */
    GetOrderBook(req) {
        return this._sendCmd(ftCmdID.QotGetOrderBook.cmd, req, ftCmdID.QotGetOrderBook.name);
    }
    /**
     * 获取K线
     * @param req 具体字段请参考Qot_GetKL.proto协议
     */
    GetKL(req) {
        return this._sendCmd(ftCmdID.QotGetKL.cmd, req, ftCmdID.QotGetKL.name);
    }
    /**
     * 获取分时
     * @param req 具体字段请参考Qot_GetRT.proto协议
     */
    GetRT(req) {
        return this._sendCmd(ftCmdID.QotGetRT.cmd, req, ftCmdID.QotGetRT.name);
    }
    /**
     * 获取经纪队列
     * @param req 具体字段请参考Qot_GetBroker.proto协议
     */
    GetBroker(req) {
        return this._sendCmd(ftCmdID.QotGetBroker.cmd, req, ftCmdID.QotGetBroker.name);
    }
    // 添加更多行情相关方法...
    /**
     * 在线请求历史复权信息，不读本地历史数据DB
     * @param req 具体字段请参考Qot_RequestRehab.proto协议
     */
    RequestRehab(req) {
        return this._sendCmd(ftCmdID.QotRequestRehab.cmd, req, ftCmdID.QotRequestRehab.name);
    }
    /**
     * 在线请求历史K线，不读本地历史数据DB
     * @param req 具体字段请参考Qot_RequestHistoryKL.proto协议
     */
    RequestHistoryKL(req) {
        return this._sendCmd(ftCmdID.QotRequestHistoryKL.cmd, req, ftCmdID.QotRequestHistoryKL.name);
    }
    /**
     * 获取历史K线已经用掉的额度
     * @param req 具体字段请参考Qot_RequestHistoryKLQuota.proto协议
     */
    RequestHistoryKLQuota(req) {
        return this._sendCmd(ftCmdID.QotRequestHistoryKLQuota.cmd, req, ftCmdID.QotRequestHistoryKLQuota.name);
    }
    /**
     * 获取交易日
     * @param req 具体字段请参考Qot_GetTradeDate.proto协议
     */
    GetTradeDate(req) {
        return this._sendCmd(ftCmdID.QotGetTradeDate.cmd, req, ftCmdID.QotGetTradeDate.name);
    }
    /**
     * 获取静态信息
     * @param req 具体字段请参考Qot_GetStaticInfo.proto协议
     */
    GetStaticInfo(req) {
        return this._sendCmd(ftCmdID.QotGetStaticInfo.cmd, req, ftCmdID.QotGetStaticInfo.name);
    }
    /**
     * 获取股票快照
     * @param req 具体字段请参考Qot_GetSecuritySnapshot.proto协议
     */
    GetSecuritySnapshot(req) {
        return this._sendCmd(ftCmdID.QotGetSecuritySnapshot.cmd, req, ftCmdID.QotGetSecuritySnapshot.name);
    }
    //=============================================
    /**
     * 发送数据
     */
    _sendCmd(cmd, buff, name) {
        return new Promise((resolve, reject) => {
            if (cmd == null || name == null || name == undefined) {
                reject('error parameter');
                return;
            }
            if (this.websock == null || this.websock == undefined) {
                reject('websock is null');
                return;
            }
            const PBMessageRequest = protoRoot.lookup(name + '.Request');
            const message = PBMessageRequest.encode(PBMessageRequest.create(buff)).finish();
            this.websock
                .sendBuff(cmd, message)
                .then((response) => {
                if (response.buff) {
                    const PBMessageResponse = protoRoot.lookup(name + '.Response');
                    const buf = new Uint8Array(response.buff);
                    const ResponseObj = PBMessageResponse.decode(buf);
                    if (ResponseObj.retType != null &&
                        ResponseObj.retType != undefined &&
                        ResponseObj.retType === 0) {
                        resolve(ResponseObj);
                    }
                    else {
                        reject(ResponseObj);
                    }
                }
            })
                .catch((error) => {
                reject(error);
            });
        });
    }
    stop() {
        if (this.websock != null && this.websock != undefined) {
            this.websock.unregPushCallback(this);
        }
    }
    _onPush(cmd, response) {
        if (response && cmd) {
            if (this.onPush && typeof this.onPush === 'function') {
                if (response.error === 0 && response.buff) {
                    const obj = FutuWebSocket.findCmdObj(cmd);
                    if (obj != null && obj.name) {
                        const NotifyPBMessageResponse = protoRoot.lookup(obj.name + '.Response');
                        const buf = new Uint8Array(response.buff);
                        const notifyObj = NotifyPBMessageResponse.decode(buf);
                        this.onPush(cmd, notifyObj);
                    }
                }
            }
        }
    }
}
//# sourceMappingURL=FutuWebSocket.js.map