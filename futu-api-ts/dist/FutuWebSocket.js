import FutuWebSocketBase from './FutuWebSocketBase';
import { ftCmdID } from './constants';
import protobuf from 'protobufjs';
import { createHash } from 'crypto';
import Long from 'long';
import protoRoot from './proto.js';
// 配置 protobuf
protobuf.util.Long = Long;
protobuf.configure();
var FutuWebSocket = /** @class */ (function () {
    function FutuWebSocket() {
        this.websock = null;
        this.onlogin = null;
        this.onPush = null;
    }
    FutuWebSocket.findCmdObj = function (cmd) {
        for (var key in ftCmdID) {
            var cmdInfo = ftCmdID[key];
            if ((cmdInfo === null || cmdInfo === void 0 ? void 0 : cmdInfo.cmd) === cmd) {
                return cmdInfo;
            }
        }
        return null;
    };
    FutuWebSocket.prototype.start = function (ip, port, ssl, key) {
        var _this = this;
        if (this.websock == null || this.websock == undefined) {
            this.websock = new FutuWebSocketBase();
            this.websock.regPushCallback(this, this._onPush.bind(this));
        }
        else {
            this.websock.close();
        }
        this.websock.setWsConfig(ip, port, ssl);
        var message = null;
        var keyMD5 = '';
        if (key) {
            keyMD5 = createHash('md5').update(key).digest('hex');
        }
        var InitWebSocketPBMessageRequest = protoRoot.lookup('InitWebSocket.Request');
        var initWebSocketReq = {
            c2s: {
                websocketKey: keyMD5,
                programmingLanguage: 'TypeScript'
            }
        };
        message = InitWebSocketPBMessageRequest.encode(InitWebSocketPBMessageRequest.create(initWebSocketReq)).finish();
        this.websock.initWebSocket(message || undefined);
        this.websock.onlogin = function (ret, msg) {
            if (_this.onlogin && typeof _this.onlogin === 'function') {
                _this.onlogin(ret, msg);
            }
        };
    };
    FutuWebSocket.prototype.getConnID = function () {
        var _a;
        return ((_a = this.websock) === null || _a === void 0 ? void 0 : _a.connID) || 0;
    };
    /**
     * 订阅，反订阅
     * @param req 请求包，具体字段请参考Qot_Sub.proto协议
     */
    FutuWebSocket.prototype.Sub = function (req) {
        return this._sendCmd(ftCmdID.QotSub.cmd, req, ftCmdID.QotSub.name);
    };
    /**
     * 获取交易帐号列表
     * @param req 请求包，具体字段请参考Trd_GetAccList.proto协议
     */
    FutuWebSocket.prototype.GetAccList = function (req) {
        return this._sendCmd(ftCmdID.TrdGetAccList.cmd, req, ftCmdID.TrdGetAccList.name);
    };
    /**
     * 解锁，针对OpenD解锁一次即可
     * @param req 请求包，具体字段请参考Trd_UnlockTrade.proto协议
     */
    FutuWebSocket.prototype.UnlockTrade = function (req) {
        return this._sendCmd(ftCmdID.TrdUnlockTrade.cmd, req, ftCmdID.TrdUnlockTrade.name);
    };
    /**
     * 订阅接收推送数据的交易账户
     * @param req 请求包，具体字段请参考Trd_SubAccPush.proto协议
     */
    FutuWebSocket.prototype.SubAccPush = function (req) {
        return this._sendCmd(ftCmdID.TrdSubAccPush.cmd, req, ftCmdID.TrdSubAccPush.name);
    };
    /**
     * 获取账户资金
     * @param req 请求包，具体字段请参考Trd_GetFunds.proto协议
     */
    FutuWebSocket.prototype.GetFunds = function (req) {
        return this._sendCmd(ftCmdID.TrdGetFunds.cmd, req, ftCmdID.TrdGetFunds.name);
    };
    /**
     * 获取账户持仓
     * @param req 请求包，具体字段请参考Trd_GetPositionList.proto协议
     */
    FutuWebSocket.prototype.GetPositionList = function (req) {
        return this._sendCmd(ftCmdID.TrdGetPositionList.cmd, req, ftCmdID.TrdGetPositionList.name);
    };
    /**
     * 获取最大交易数量
     * @param req 请求包，具体字段请参考Trd_GetMaxTrdQtys.proto协议
     */
    FutuWebSocket.prototype.GetMaxTrdQtys = function (req) {
        return this._sendCmd(ftCmdID.TrdGetMaxTrdQtys.cmd, req, ftCmdID.TrdGetMaxTrdQtys.name);
    };
    /**
     * 获取当日订单列表
     * @param req 请求包，具体字段请参考Trd_GetOrderList.proto协议
     */
    FutuWebSocket.prototype.GetOrderList = function (req) {
        return this._sendCmd(ftCmdID.TrdGetOrderList.cmd, req, ftCmdID.TrdGetOrderList.name);
    };
    /**
     * 下单
     * @param req 请求包，具体字段请参考Trd_PlaceOrder.proto协议，PacketID不需填写，发送时接口会填
     */
    FutuWebSocket.prototype.PlaceOrder = function (req) {
        return this._sendCmd(ftCmdID.TrdPlaceOrder.cmd, req, ftCmdID.TrdPlaceOrder.name);
    };
    /**
     * 修改订单
     * @param req 请求包，具体字段请参考Trd_ModifyOrder.proto协议，PacketID不需填写，发送时接口会填
     */
    FutuWebSocket.prototype.ModifyOrder = function (req) {
        return this._sendCmd(ftCmdID.TrdModifyOrder.cmd, req, ftCmdID.TrdModifyOrder.name);
    };
    /**
     * 获取当日成交列表
     * @param req 请求包，具体字段请参考Trd_GetOrderFillList.proto协议
     */
    FutuWebSocket.prototype.GetOrderFillList = function (req) {
        return this._sendCmd(ftCmdID.TrdGetOrderFillList.cmd, req, ftCmdID.TrdGetOrderFillList.name);
    };
    /**
     * 获取历史订单列表
     * @param req 请求包，具体字段请参考Trd_GetHistoryOrderList.proto协议
     */
    FutuWebSocket.prototype.GetHistoryOrderList = function (req) {
        return this._sendCmd(ftCmdID.TrdGetHistoryOrderList.cmd, req, ftCmdID.TrdGetHistoryOrderList.name);
    };
    /**
     * 获取历史成交列表
     * @param req 请求包，具体字段请参考Trd_GetHistoryOrderFillList.proto协议
     */
    FutuWebSocket.prototype.GetHistoryOrderFillList = function (req) {
        return this._sendCmd(ftCmdID.TrdGetHistoryOrderFillList.cmd, req, ftCmdID.TrdGetHistoryOrderFillList.name);
    };
    /**
     * 获取融资融券数据
     * @param req 请求包，具体字段请参考Trd_GetMarginRatio.proto协议
     */
    FutuWebSocket.prototype.GetMarginRatio = function (req) {
        return this._sendCmd(ftCmdID.TrdGetMarginRatio.cmd, req, ftCmdID.TrdGetMarginRatio.name);
    };
    /**
     * 获取订单收费明细数据
     * @param req 请求包，具体字段请参考Trd_GetOrderFee.proto协议
     */
    FutuWebSocket.prototype.GetOrderFee = function (req) {
        return this._sendCmd(ftCmdID.TrdGetOrderFee.cmd, req, ftCmdID.TrdGetOrderFee.name);
    };
    /**
     * 获取资金流水
     * @param req 请求包，具体字段请参考Trd_FlowSummary.proto协议
     */
    FutuWebSocket.prototype.GetFlowSummary = function (req) {
        return this._sendCmd(ftCmdID.TrdFlowSummary.cmd, req, ftCmdID.TrdFlowSummary.name);
    };
    /**
     * 请求全局状态
     * @param req 具体字段请参考GetGlobalState.proto协议
     */
    FutuWebSocket.prototype.GetGlobalState = function (req) {
        return this._sendCmd(ftCmdID.GetGlobalState.cmd, req, ftCmdID.GetGlobalState.name);
    };
    /**
     * 注册推送
     * @param req 具体字段请参考Qot_RegQotPush.proto协议
     */
    FutuWebSocket.prototype.RegQotPush = function (req) {
        return this._sendCmd(ftCmdID.QotRegQotPush.cmd, req, ftCmdID.QotRegQotPush.name);
    };
    /**
     * 获取订阅信息
     * @param req 具体字段请参考Qot_GetSubInfo.proto协议
     */
    FutuWebSocket.prototype.GetSubInfo = function (req) {
        return this._sendCmd(ftCmdID.QotGetSubInfo.cmd, req, ftCmdID.QotGetSubInfo.name);
    };
    /**
     * 获取逐笔
     * @param req 具体字段请参考Qot_GetTicker.proto协议
     */
    FutuWebSocket.prototype.GetTicker = function (req) {
        return this._sendCmd(ftCmdID.QotGetTicker.cmd, req, ftCmdID.QotGetTicker.name);
    };
    /**
     * 获取报价
     * @param req 具体字段请参考Qot_GetBasicQot.proto协议
     */
    FutuWebSocket.prototype.GetBasicQot = function (req) {
        return this._sendCmd(ftCmdID.QotGetBasicQot.cmd, req, ftCmdID.QotGetBasicQot.name);
    };
    /**
     * 获取摆盘
     * @param req 具体字段请参考Qot_GetOrderBook.proto协议
     */
    FutuWebSocket.prototype.GetOrderBook = function (req) {
        return this._sendCmd(ftCmdID.QotGetOrderBook.cmd, req, ftCmdID.QotGetOrderBook.name);
    };
    /**
     * 获取K线
     * @param req 具体字段请参考Qot_GetKL.proto协议
     */
    FutuWebSocket.prototype.GetKL = function (req) {
        return this._sendCmd(ftCmdID.QotGetKL.cmd, req, ftCmdID.QotGetKL.name);
    };
    /**
     * 获取分时
     * @param req 具体字段请参考Qot_GetRT.proto协议
     */
    FutuWebSocket.prototype.GetRT = function (req) {
        return this._sendCmd(ftCmdID.QotGetRT.cmd, req, ftCmdID.QotGetRT.name);
    };
    /**
     * 获取经纪队列
     * @param req 具体字段请参考Qot_GetBroker.proto协议
     */
    FutuWebSocket.prototype.GetBroker = function (req) {
        return this._sendCmd(ftCmdID.QotGetBroker.cmd, req, ftCmdID.QotGetBroker.name);
    };
    // 添加更多行情相关方法...
    /**
     * 在线请求历史复权信息，不读本地历史数据DB
     * @param req 具体字段请参考Qot_RequestRehab.proto协议
     */
    FutuWebSocket.prototype.RequestRehab = function (req) {
        return this._sendCmd(ftCmdID.QotRequestRehab.cmd, req, ftCmdID.QotRequestRehab.name);
    };
    /**
     * 在线请求历史K线，不读本地历史数据DB
     * @param req 具体字段请参考Qot_RequestHistoryKL.proto协议
     */
    FutuWebSocket.prototype.RequestHistoryKL = function (req) {
        return this._sendCmd(ftCmdID.QotRequestHistoryKL.cmd, req, ftCmdID.QotRequestHistoryKL.name);
    };
    /**
     * 获取历史K线已经用掉的额度
     * @param req 具体字段请参考Qot_RequestHistoryKLQuota.proto协议
     */
    FutuWebSocket.prototype.RequestHistoryKLQuota = function (req) {
        return this._sendCmd(ftCmdID.QotRequestHistoryKLQuota.cmd, req, ftCmdID.QotRequestHistoryKLQuota.name);
    };
    /**
     * 获取交易日
     * @param req 具体字段请参考Qot_GetTradeDate.proto协议
     */
    FutuWebSocket.prototype.GetTradeDate = function (req) {
        return this._sendCmd(ftCmdID.QotGetTradeDate.cmd, req, ftCmdID.QotGetTradeDate.name);
    };
    /**
     * 获取静态信息
     * @param req 具体字段请参考Qot_GetStaticInfo.proto协议
     */
    FutuWebSocket.prototype.GetStaticInfo = function (req) {
        return this._sendCmd(ftCmdID.QotGetStaticInfo.cmd, req, ftCmdID.QotGetStaticInfo.name);
    };
    /**
     * 获取股票快照
     * @param req 具体字段请参考Qot_GetSecuritySnapshot.proto协议
     */
    FutuWebSocket.prototype.GetSecuritySnapshot = function (req) {
        return this._sendCmd(ftCmdID.QotGetSecuritySnapshot.cmd, req, ftCmdID.QotGetSecuritySnapshot.name);
    };
    //=============================================
    /**
     * 发送数据
     */
    FutuWebSocket.prototype._sendCmd = function (cmd, buff, name) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            if (cmd == null || name == null || name == undefined) {
                reject('error parameter');
                return;
            }
            if (_this.websock == null || _this.websock == undefined) {
                reject('websock is null');
                return;
            }
            var PBMessageRequest = protoRoot.lookup(name + '.Request');
            var message = PBMessageRequest.encode(PBMessageRequest.create(buff)).finish();
            _this.websock
                .sendBuff(cmd, message)
                .then(function (response) {
                if (response.buff) {
                    var PBMessageResponse = protoRoot.lookup(name + '.Response');
                    var buf = new Uint8Array(response.buff);
                    var ResponseObj = PBMessageResponse.decode(buf);
                    if (ResponseObj.retType != null &&
                        ResponseObj.retType != undefined &&
                        ResponseObj.retType === 0) {
                        resolve(ResponseObj);
                    }
                    else {
                        reject(ResponseObj);
                    }
                }
            })["catch"](function (error) {
                reject(error);
            });
        });
    };
    FutuWebSocket.prototype.stop = function () {
        if (this.websock != null && this.websock != undefined) {
            this.websock.unregPushCallback(this);
        }
    };
    FutuWebSocket.prototype._onPush = function (cmd, response) {
        if (response && cmd) {
            if (this.onPush && typeof this.onPush === 'function') {
                if (response.error === 0 && response.buff) {
                    var obj = FutuWebSocket.findCmdObj(cmd);
                    if (obj != null && obj.name) {
                        var NotifyPBMessageResponse = protoRoot.lookup(obj.name + '.Response');
                        var buf = new Uint8Array(response.buff);
                        var notifyObj = NotifyPBMessageResponse.decode(buf);
                        this.onPush(cmd, notifyObj);
                    }
                }
            }
        }
    };
    return FutuWebSocket;
}());
export default FutuWebSocket;
//# sourceMappingURL=FutuWebSocket.js.map