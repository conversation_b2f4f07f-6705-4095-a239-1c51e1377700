import { test } from 'node:test';
import assert from 'node:assert';
import { ftCmdID } from '../constants.js';
test('ftCmdID should contain expected commands', function () {
    assert.ok(ftCmdID);
    assert.ok(ftCmdID.TrdGetAccList);
    assert.strictEqual(ftCmdID.TrdGetAccList.cmd, 2001);
    assert.strictEqual(ftCmdID.TrdGetAccList.name, 'Trd_GetAccList');
});
test('ftCmdID should have all required trading commands', function () {
    var requiredCommands = [
        'TrdGetAccList',
        'TrdUnlockTrade',
        'TrdGetFunds',
        'TrdGetPositionList',
        'TrdPlaceOrder',
    ];
    for (var _i = 0, requiredCommands_1 = requiredCommands; _i < requiredCommands_1.length; _i++) {
        var cmd = requiredCommands_1[_i];
        var cmdInfo = ftCmdID[cmd];
        assert.ok(cmdInfo, "Command " + cmd + " should exist");
        assert.ok(typeof cmdInfo.cmd === 'number', "Command " + cmd + " should have numeric cmd");
        assert.ok(typeof cmdInfo.name === 'string', "Command " + cmd + " should have string name");
    }
});
test('ftCmdID should have all required quote commands', function () {
    var requiredCommands = ['QotSub', 'QotGetBasicQot', 'QotGetKL', 'QotGetOrderBook'];
    for (var _i = 0, requiredCommands_2 = requiredCommands; _i < requiredCommands_2.length; _i++) {
        var cmd = requiredCommands_2[_i];
        var cmdInfo = ftCmdID[cmd];
        assert.ok(cmdInfo, "Command " + cmd + " should exist");
        assert.ok(typeof cmdInfo.cmd === 'number', "Command " + cmd + " should have numeric cmd");
        assert.ok(typeof cmdInfo.name === 'string', "Command " + cmd + " should have string name");
    }
});
//# sourceMappingURL=basic.test.js.map