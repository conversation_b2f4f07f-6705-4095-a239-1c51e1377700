/**
 * @fileOverview 富途OpenD websocket TypeScript接口封装
 * <AUTHOR>
 * @version 1.0
 */
import { format } from 'util';
import ByteBuffer from 'bytebuffer';
import protoRoot from './proto.js';
var ftApiCmdID = {
    Init: 1,
    OpenDisConnect: 2
};
var ftWebsocketRecvError = {
    ErrorSign: 1
};
// const ftWebsocketError = {
//   WEBSOCKET_ERROR_TIMEOUT: 1 as const,
//   WEBSOCKET_ERROR_OPEND_TIMEOUT: 2 as const,
//   WEBSOCKET_ERROR_DISCONNECT: 3 as const,
// };
var ftWebsocketSection = 1;
var ftWebsocketHeadLength = 44;
var ftWebsocketHeadSign = 'ft-v1.0';
/**
 * @name FutuWebSocketBase
 * @class websocket封装工具类
 * @constructor FutuWebSocketBase
 * @classdesc websocket封装工具类
 */
var FutuWebSocketBase = /** @class */ (function () {
    function FutuWebSocketBase() {
        this.websock = null;
        this.wsuri = 'wss://127.0.0.1:8080';
        this.initOpenDConfigBuff = null;
        this.timeout = 5000; // 5s
        this.reconnectTimeout = 1000; // 每秒重连一次
        this.promisePool = {};
        this.state = {
            closing: false,
            login: false
        };
        this.pushCalls = new Map();
        this.reconnectTimer = null;
        this.connID = 0;
    }
    /**
     * 初始化设置websocket连接的ip和端口号
     * @param ip ip地址，如127.0.0.1
     * @param port 端口号，如 8080
     * @param ssl 是否使用SSL
     */
    FutuWebSocketBase.prototype.setWsConfig = function (ip, port, ssl) {
        if (ip !== null && port !== null) {
            var wsuri = void 0;
            if (ssl === false) {
                wsuri = format('ws://%s:%d', ip, port);
            }
            else {
                wsuri = format('wss://%s:%d', ip, port);
            }
            if (wsuri !== this.wsuri) {
                this.close();
                this.wsuri = wsuri;
            }
        }
    };
    FutuWebSocketBase.prototype.packBuff = function (cmd, section, buff) {
        var pbuff = new ByteBuffer();
        pbuff.writeUTF8String(ftWebsocketHeadSign);
        var completeLength = 8 - ByteBuffer.calculateString(ftWebsocketHeadSign);
        if (completeLength > 0) {
            for (var i = 0; i < completeLength; i++) {
                pbuff.writeByte(0);
            }
        }
        pbuff.writeUint32(cmd);
        pbuff.writeUint64(section);
        if (buff instanceof Uint8Array) {
            pbuff.append(buff);
        }
        else if (buff instanceof ArrayBuffer) {
            pbuff.append(new Uint8Array(buff));
        }
        pbuff.flip();
        return pbuff.toArrayBuffer();
    };
    // 解包函数
    FutuWebSocketBase.prototype.unpackBuff = function (buff) {
        if (buff instanceof ArrayBuffer) {
            var pbuff = new ByteBuffer(buff.byteLength, false);
            var result = {};
            pbuff.append(buff);
            pbuff.flip();
            result.sign = pbuff.readUTF8String(8);
            result.cmd = pbuff.readUint32();
            result.section = pbuff.readUint64().toNumber();
            result.error = pbuff.readInt32();
            result.errmsg = pbuff.readUTF8String(20).replace(/\0/g, '');
            if (buff.byteLength > ftWebsocketHeadLength) {
                var data = pbuff.readBytes(buff.byteLength - ftWebsocketHeadLength);
                result.buff = data.toArrayBuffer();
            }
            return result;
        }
        return null;
    };
    /**
     * 数据发送，适用于一应一答的场景，发送数据后，必然回包或超时
     * @param cmd 命令字，如1001
     * @param buff 发送的二进制数据，Uint8Array或者ArrayBuffer格式
     * @param timeout 协议超时时间，默认5s超时
     * @returns Promise 返回响应数据或错误
     */
    FutuWebSocketBase.prototype.sendBuff = function (cmd, buff, timeout) {
        var that = this;
        // 超时回调
        function timeoutCallBack(section, timeoutMs) {
            return new Promise(function (_, reject) {
                var timer = setTimeout(function () {
                    if (that.promisePool[section] != null) {
                        reject('timeout');
                        console.log('section:', section, ' timeout');
                        delete that.promisePool[section];
                    }
                }, timeoutMs);
                if (that.promisePool[section]) {
                    that.promisePool[section].timer = timer;
                }
            });
        }
        // 数据接收回调填充数据
        function requestCallBack(section) {
            return new Promise(function (resolve, reject) {
                if (that.promisePool[section]) {
                    that.promisePool[section].section = section;
                    that.promisePool[section].resolve = resolve;
                    that.promisePool[section].reject = reject;
                    that.promisePool[section].socket = that.websock;
                }
            });
        }
        var section = 0;
        if (this.websock && this.websock.readyState === WebSocket.OPEN) {
            if (this.state.login || cmd === ftApiCmdID.Init) {
                section = ++ftWebsocketSection;
                this.promisePool[section] = {
                    section: 0,
                    resolve: function () { },
                    reject: function () { },
                    socket: this.websock
                };
                var arrayBuff = this.packBuff(cmd, section, buff);
                this.websock.send(arrayBuff);
                if (timeout == null || timeout === undefined) {
                    timeout = this.timeout;
                }
                return Promise.race([requestCallBack(section), timeoutCallBack(section, timeout)]);
            }
        }
        return Promise.reject('error websock not ready');
    };
    FutuWebSocketBase.prototype.recvBuff = function (buff) {
        var result = this.unpackBuff(buff);
        var error = 0;
        if (result !== null && result.sign !== null && result.sign !== undefined) {
            if (result.sign.indexOf(ftWebsocketHeadSign) === -1) {
                console.log(result.sign, '||', ftWebsocketHeadSign);
                error = ftWebsocketRecvError.ErrorSign;
                return { error: error, result: result };
            }
            delete result.sign;
            var section = result.section;
            delete result.section;
            var req = this.promisePool[section];
            if (req != null) {
                req.resolve(result);
                delete this.promisePool[section];
                if (req.timer != null) {
                    clearTimeout(req.timer);
                    req.timer = undefined;
                }
            }
            else {
                this.pushCalls.forEach(function (f) {
                    f(result.cmd, result);
                });
            }
        }
        return { error: error, result: result };
    };
    /**
     * 注册push回调，回调链可以注册多个，务必需要反注册
     * @param key 用于反注册时候使用的key
     * @param func 回调函数，需要满足接收一个参数，参数是传回的整个包
     */
    FutuWebSocketBase.prototype.regPushCallback = function (key, func) {
        this.pushCalls.set(key, func);
    };
    /**
     * 反注册push回调
     * @param key 用于反注册时候使用的key
     */
    FutuWebSocketBase.prototype.unregPushCallback = function (key) {
        this.pushCalls["delete"](key);
    };
    /**
     * 关闭socket
     */
    FutuWebSocketBase.prototype.close = function () {
        this.state.closing = true;
        this.killReconnectTimer();
        if (this.websock) {
            this.websock.close();
        }
        this.rejectAll();
    };
    FutuWebSocketBase.prototype.rejectAll = function () {
        if (this.promisePool !== null && this.promisePool !== undefined) {
            for (var _i = 0, _a = Object.values(this.promisePool); _i < _a.length; _i++) {
                var req = _a[_i];
                if (req != null) {
                    if (req.reject != null) {
                        req.reject('close');
                    }
                    if (req.timer != null) {
                        clearTimeout(req.timer);
                        req.timer = undefined;
                    }
                }
            }
        }
        this.promisePool = {};
    };
    FutuWebSocketBase.prototype.reconnect = function (timeout) {
        var _this = this;
        this.killReconnectTimer();
        this.reconnectTimer = setTimeout(function () {
            if (_this.websock == null || _this.websock.readyState !== WebSocket.OPEN) {
                _this.websock = null;
                _this.initWebSocket();
                _this.reconnectTimer = null;
            }
        }, timeout);
    };
    FutuWebSocketBase.prototype.isReadyConnect = function () {
        if (this.websock == null || this.websock.readyState !== WebSocket.OPEN) {
            return false;
        }
        return this.state.login;
    };
    FutuWebSocketBase.prototype.killReconnectTimer = function () {
        if (this.reconnectTimer !== undefined && this.reconnectTimer !== null) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    };
    /**
     * 初始化websocket
     * @param configBuffer 参考InitWebSocket.proto，非必填字段
     */
    FutuWebSocketBase.prototype.initWebSocket = function (configBuffer) {
        var _this = this;
        if (this.websock != null) {
            console.debug('websock is not null');
            this.websock.onmessage = null;
            this.websock.onopen = null;
            this.websock.onerror = null;
            this.websock.onclose = null;
            delete this.websock;
        }
        this.killReconnectTimer();
        this.promisePool = {};
        this.state.closing = false;
        if (configBuffer !== null && configBuffer !== undefined && configBuffer instanceof Uint8Array) {
            this.initOpenDConfigBuff = configBuffer;
        }
        this.websock = new WebSocket(this.wsuri);
        this.websock.binaryType = 'arraybuffer';
        this.websock.onmessage = function (e) {
            var msg = _this.recvBuff(e.data);
            if (_this.onmessage !== undefined && typeof _this.onmessage === 'function') {
                _this.onmessage(msg);
            }
        };
        this.websock.onopen = function () {
            _this.sendBuff(ftApiCmdID.Init, _this.initOpenDConfigBuff || undefined, 20000)
                .then(function (response) {
                if (response.buff != null && response.error === 0) {
                    // 解包获取连接ID
                    var initInfo = protoRoot.lookup('InitWebSocket.Response');
                    var buf = new Uint8Array(response.buff);
                    var initResult = initInfo.decode(buf);
                    _this.connID = initResult.s2c.connID;
                    console.debug('登录成功');
                    if (!_this.state.closing) {
                        _this.state.login = true;
                        if (_this.onlogin !== undefined && typeof _this.onlogin === 'function') {
                            _this.onlogin(true, response);
                        }
                    }
                }
                else {
                    if (_this.onlogin !== undefined && typeof _this.onlogin === 'function') {
                        _this.onlogin(false, response.error);
                    }
                    _this.state.login = false;
                    _this.close();
                }
            })["catch"](function (error) {
                console.debug('login error:', error);
                if (_this.onlogin !== undefined && typeof _this.onlogin === 'function') {
                    _this.onlogin(false, error);
                }
                _this.state.login = false;
                _this.close();
            });
        };
        this.websock.onerror = function (e) {
            console.debug('发生异常', e);
            if (_this.onerror !== undefined && typeof _this.onerror === 'function') {
                _this.onerror(e);
            }
        };
        this.websock.onclose = function (e) {
            console.debug('断开连接', e);
            _this.rejectAll();
            if (!_this.state.closing) {
                _this.reconnect(_this.reconnectTimeout);
            }
            _this.state.login = false;
            if (_this.onclose !== undefined && typeof _this.onclose === 'function') {
                _this.onclose(e);
            }
        };
        return this;
    };
    return FutuWebSocketBase;
}());
export default FutuWebSocketBase;
//# sourceMappingURL=FutuWebSocketBase.js.map