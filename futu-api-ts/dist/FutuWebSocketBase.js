/**
 * @fileOverview 富途OpenD websocket TypeScript接口封装
 * <AUTHOR>
 * @version 1.0
 */
import { format } from 'util';
import ByteBuffer from 'bytebuffer';
import protoRoot from './proto.js';
const ftApiCmdID = {
    Init: 1,
    OpenDisConnect: 2,
};
const ftWebsocketRecvError = {
    ErrorSign: 1,
};
// const ftWebsocketError = {
//   WEBSOCKET_ERROR_TIMEOUT: 1 as const,
//   WEBSOCKET_ERROR_OPEND_TIMEOUT: 2 as const,
//   WEBSOCKET_ERROR_DISCONNECT: 3 as const,
// };
let ftWebsocketSection = 1;
const ftWebsocketHeadLength = 44;
const ftWebsocketHeadSign = 'ft-v1.0';
/**
 * @name FutuWebSocketBase
 * @class websocket封装工具类
 * @constructor FutuWebSocketBase
 * @classdesc websocket封装工具类
 */
export default class FutuWebSocketBase {
    websock = null;
    wsuri = 'wss://127.0.0.1:8080';
    initOpenDConfigBuff = null;
    timeout = 5000; // 5s
    reconnectTimeout = 1000; // 每秒重连一次
    promisePool = {};
    state = {
        closing: false,
        login: false,
    };
    pushCalls = new Map();
    reconnectTimer = null;
    connID = 0;
    // 回调函数
    onlogin;
    onmessage;
    onerror;
    onclose;
    /**
     * 初始化设置websocket连接的ip和端口号
     * @param ip ip地址，如127.0.0.1
     * @param port 端口号，如 8080
     * @param ssl 是否使用SSL
     */
    setWsConfig(ip, port, ssl) {
        if (ip !== null && port !== null) {
            let wsuri;
            if (ssl === false) {
                wsuri = format('ws://%s:%d', ip, port);
            }
            else {
                wsuri = format('wss://%s:%d', ip, port);
            }
            if (wsuri !== this.wsuri) {
                this.close();
                this.wsuri = wsuri;
            }
        }
    }
    packBuff(cmd, section, buff) {
        const pbuff = new ByteBuffer();
        pbuff.writeUTF8String(ftWebsocketHeadSign);
        const completeLength = 8 - ByteBuffer.calculateString(ftWebsocketHeadSign);
        if (completeLength > 0) {
            for (let i = 0; i < completeLength; i++) {
                pbuff.writeByte(0);
            }
        }
        pbuff.writeUint32(cmd);
        pbuff.writeUint64(section);
        if (buff instanceof Uint8Array) {
            pbuff.append(buff);
        }
        else if (buff instanceof ArrayBuffer) {
            pbuff.append(new Uint8Array(buff));
        }
        pbuff.flip();
        return pbuff.toArrayBuffer();
    }
    // 解包函数
    unpackBuff(buff) {
        if (buff instanceof ArrayBuffer) {
            const pbuff = new ByteBuffer(buff.byteLength, false);
            const result = {};
            pbuff.append(buff);
            pbuff.flip();
            result.sign = pbuff.readUTF8String(8);
            result.cmd = pbuff.readUint32();
            result.section = pbuff.readUint64().toNumber();
            result.error = pbuff.readInt32();
            result.errmsg = pbuff.readUTF8String(20).replace(/\0/g, '');
            if (buff.byteLength > ftWebsocketHeadLength) {
                const data = pbuff.readBytes(buff.byteLength - ftWebsocketHeadLength);
                result.buff = data.toArrayBuffer();
            }
            return result;
        }
        return null;
    }
    /**
     * 数据发送，适用于一应一答的场景，发送数据后，必然回包或超时
     * @param cmd 命令字，如1001
     * @param buff 发送的二进制数据，Uint8Array或者ArrayBuffer格式
     * @param timeout 协议超时时间，默认5s超时
     * @returns Promise 返回响应数据或错误
     */
    sendBuff(cmd, buff, timeout) {
        const that = this;
        // 超时回调
        function timeoutCallBack(section, timeoutMs) {
            return new Promise((_, reject) => {
                const timer = setTimeout(() => {
                    if (that.promisePool[section] != null) {
                        reject('timeout');
                        console.log('section:', section, ' timeout');
                        delete that.promisePool[section];
                    }
                }, timeoutMs);
                if (that.promisePool[section]) {
                    that.promisePool[section].timer = timer;
                }
            });
        }
        // 数据接收回调填充数据
        function requestCallBack(section) {
            return new Promise((resolve, reject) => {
                if (that.promisePool[section]) {
                    that.promisePool[section].section = section;
                    that.promisePool[section].resolve = resolve;
                    that.promisePool[section].reject = reject;
                    that.promisePool[section].socket = that.websock;
                }
            });
        }
        let section = 0;
        if (this.websock && this.websock.readyState === WebSocket.OPEN) {
            if (this.state.login || cmd === ftApiCmdID.Init) {
                section = ++ftWebsocketSection;
                this.promisePool[section] = {
                    section: 0,
                    resolve: () => { },
                    reject: () => { },
                    socket: this.websock,
                };
                const arrayBuff = this.packBuff(cmd, section, buff);
                this.websock.send(arrayBuff);
                if (timeout == null || timeout === undefined) {
                    timeout = this.timeout;
                }
                return Promise.race([requestCallBack(section), timeoutCallBack(section, timeout)]);
            }
        }
        return Promise.reject('error websock not ready');
    }
    recvBuff(buff) {
        const result = this.unpackBuff(buff);
        let error = 0;
        if (result !== null && result.sign !== null && result.sign !== undefined) {
            if (result.sign.indexOf(ftWebsocketHeadSign) === -1) {
                console.log(result.sign, '||', ftWebsocketHeadSign);
                error = ftWebsocketRecvError.ErrorSign;
                return { error, result };
            }
            delete result.sign;
            const section = result.section;
            delete result.section;
            const req = this.promisePool[section];
            if (req != null) {
                req.resolve(result);
                delete this.promisePool[section];
                if (req.timer != null) {
                    clearTimeout(req.timer);
                    req.timer = undefined;
                }
            }
            else {
                this.pushCalls.forEach((f) => {
                    f(result.cmd, result);
                });
            }
        }
        return { error, result };
    }
    /**
     * 注册push回调，回调链可以注册多个，务必需要反注册
     * @param key 用于反注册时候使用的key
     * @param func 回调函数，需要满足接收一个参数，参数是传回的整个包
     */
    regPushCallback(key, func) {
        this.pushCalls.set(key, func);
    }
    /**
     * 反注册push回调
     * @param key 用于反注册时候使用的key
     */
    unregPushCallback(key) {
        this.pushCalls.delete(key);
    }
    /**
     * 关闭socket
     */
    close() {
        this.state.closing = true;
        this.killReconnectTimer();
        if (this.websock) {
            this.websock.close();
        }
        this.rejectAll();
    }
    rejectAll() {
        if (this.promisePool !== null && this.promisePool !== undefined) {
            for (const req of Object.values(this.promisePool)) {
                if (req != null) {
                    if (req.reject != null) {
                        req.reject('close');
                    }
                    if (req.timer != null) {
                        clearTimeout(req.timer);
                        req.timer = undefined;
                    }
                }
            }
        }
        this.promisePool = {};
    }
    reconnect(timeout) {
        this.killReconnectTimer();
        this.reconnectTimer = setTimeout(() => {
            if (this.websock == null || this.websock.readyState !== WebSocket.OPEN) {
                this.websock = null;
                this.initWebSocket();
                this.reconnectTimer = null;
            }
        }, timeout);
    }
    isReadyConnect() {
        if (this.websock == null || this.websock.readyState !== WebSocket.OPEN) {
            return false;
        }
        return this.state.login;
    }
    killReconnectTimer() {
        if (this.reconnectTimer !== undefined && this.reconnectTimer !== null) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }
    /**
     * 初始化websocket
     * @param configBuffer 参考InitWebSocket.proto，非必填字段
     */
    initWebSocket(configBuffer) {
        if (this.websock != null) {
            console.debug('websock is not null');
            this.websock.onmessage = null;
            this.websock.onopen = null;
            this.websock.onerror = null;
            this.websock.onclose = null;
            delete this.websock;
        }
        this.killReconnectTimer();
        this.promisePool = {};
        this.state.closing = false;
        if (configBuffer !== null && configBuffer !== undefined && configBuffer instanceof Uint8Array) {
            this.initOpenDConfigBuff = configBuffer;
        }
        this.websock = new WebSocket(this.wsuri);
        this.websock.binaryType = 'arraybuffer';
        this.websock.onmessage = (e) => {
            const msg = this.recvBuff(e.data);
            if (this.onmessage !== undefined && typeof this.onmessage === 'function') {
                this.onmessage(msg);
            }
        };
        this.websock.onopen = () => {
            this.sendBuff(ftApiCmdID.Init, this.initOpenDConfigBuff || undefined, 20000)
                .then((response) => {
                if (response.buff != null && response.error === 0) {
                    // 解包获取连接ID
                    const initInfo = protoRoot.lookup('InitWebSocket.Response');
                    const buf = new Uint8Array(response.buff);
                    const initResult = initInfo.decode(buf);
                    this.connID = initResult.s2c.connID;
                    console.debug('登录成功');
                    if (!this.state.closing) {
                        this.state.login = true;
                        if (this.onlogin !== undefined && typeof this.onlogin === 'function') {
                            this.onlogin(true, response);
                        }
                    }
                }
                else {
                    if (this.onlogin !== undefined && typeof this.onlogin === 'function') {
                        this.onlogin(false, response.error);
                    }
                    this.state.login = false;
                    this.close();
                }
            })
                .catch((error) => {
                console.debug('login error:', error);
                if (this.onlogin !== undefined && typeof this.onlogin === 'function') {
                    this.onlogin(false, error);
                }
                this.state.login = false;
                this.close();
            });
        };
        this.websock.onerror = (e) => {
            console.debug('发生异常', e);
            if (this.onerror !== undefined && typeof this.onerror === 'function') {
                this.onerror(e);
            }
        };
        this.websock.onclose = (e) => {
            console.debug('断开连接', e);
            this.rejectAll();
            if (!this.state.closing) {
                this.reconnect(this.reconnectTimeout);
            }
            this.state.login = false;
            if (this.onclose !== undefined && typeof this.onclose === 'function') {
                this.onclose(e);
            }
        };
        return this;
    }
}
//# sourceMappingURL=FutuWebSocketBase.js.map