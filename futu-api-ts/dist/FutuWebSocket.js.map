{"version": 3, "file": "FutuWebSocket.js", "sourceRoot": "", "sources": ["../src/FutuWebSocket.ts"], "names": [], "mappings": "AAAA,OAAO,iBAAiB,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,OAAO,EAAE,MAAM,gBAAgB,CAAC;AACzC,OAAO,QAAQ,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AACpC,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,SAAS,MAAM,YAAY,CAAC;AAgBnC,cAAc;AACd,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AAC1B,QAAQ,CAAC,SAAS,EAAE,CAAC;AAErB,MAAM,CAAC,OAAO,OAAO,aAAa;IACxB,OAAO,GAA6B,IAAI,CAAC;IAC1C,OAAO,GAAyB,IAAI,CAAC;IACrC,MAAM,GAAwB,IAAI,CAAC;IAE1C,MAAM,CAAC,UAAU,CAAC,GAAW;QAC3B,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;YAC1B,MAAM,OAAO,GAAG,OAAO,CAAC,GAA2B,CAAC,CAAC;YACrD,IAAI,OAAO,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC;gBACzB,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,EAAU,EAAE,IAAY,EAAE,GAAY,EAAE,GAAY;QACxD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE,CAAC;YACtD,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAiB,EAAE,CAAC;YACvC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QAExC,IAAI,OAAO,GAAsB,IAAI,CAAC;QACtC,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,GAAG,EAAE,CAAC;YACR,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,6BAA6B,GAAG,SAAS,CAAC,MAAM,CAAC,uBAAuB,CAAQ,CAAC;QACvF,MAAM,gBAAgB,GAAG;YACvB,GAAG,EAAE;gBACH,YAAY,EAAE,MAAM;gBACpB,mBAAmB,EAAE,YAAY;aAClC;SACF,CAAC;QACF,OAAO,GAAG,6BAA6B,CAAC,MAAM,CAC5C,6BAA6B,CAAC,MAAM,CAAC,gBAAgB,CAAC,CACvD,CAAC,MAAM,EAAE,CAAC;QACX,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,IAAI,SAAS,CAAC,CAAC;QAEjD,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,GAAY,EAAE,GAAQ,EAAE,EAAE;YAChD,IAAI,IAAI,CAAC,OAAO,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;gBACvD,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,GAAG,CAAC,GAAgB;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACrE,CAAC;IAED;;;OAGG;IACH,UAAU,CAAC,GAAsB;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACnF,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,GAAuB;QACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAED;;;OAGG;IACH,UAAU,CAAC,GAAgB;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACnF,CAAC;IAED;;;OAGG;IACH,QAAQ,CAAC,GAAoB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC/E,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,GAA2B;QACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC7F,CAAC;IAED;;;OAGG;IACH,aAAa,CAAC,GAAgB;QAC5B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACzF,CAAC;IAED;;;OAGG;IACH,YAAY,CAAC,GAAgB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvF,CAAC;IAED;;;OAGG;IACH,UAAU,CAAC,GAAsB;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACnF,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,GAAgB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAED;;;OAGG;IACH,gBAAgB,CAAC,GAAgB;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC/F,CAAC;IAED;;;OAGG;IACH,mBAAmB,CAAC,GAAgB;QAClC,OAAO,IAAI,CAAC,QAAQ,CAClB,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAClC,GAAG,EACH,OAAO,CAAC,sBAAsB,CAAC,IAAI,CACpC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,uBAAuB,CAAC,GAAgB;QACtC,OAAO,IAAI,CAAC,QAAQ,CAClB,OAAO,CAAC,0BAA0B,CAAC,GAAG,EACtC,GAAG,EACH,OAAO,CAAC,0BAA0B,CAAC,IAAI,CACxC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,GAAgB;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC3F,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,GAAgB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,GAAgB;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,GAAgB;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAED;;;OAGG;IACH,UAAU,CAAC,GAAgB;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACnF,CAAC;IAED;;;OAGG;IACH,UAAU,CAAC,GAAgB;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACnF,CAAC;IAED;;;OAGG;IACH,SAAS,CAAC,GAAgB;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACjF,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,GAAgB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAED;;;OAGG;IACH,YAAY,CAAC,GAAgB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvF,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,GAAgB;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACzE,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,GAAgB;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACzE,CAAC;IAED;;;OAGG;IACH,SAAS,CAAC,GAAgB;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACjF,CAAC;IAED,gBAAgB;IAEhB;;;OAGG;IACH,YAAY,CAAC,GAAgB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvF,CAAC;IAED;;;OAGG;IACH,gBAAgB,CAAC,GAAgB;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC/F,CAAC;IAED;;;OAGG;IACH,qBAAqB,CAAC,GAAgB;QACpC,OAAO,IAAI,CAAC,QAAQ,CAClB,OAAO,CAAC,wBAAwB,CAAC,GAAG,EACpC,GAAG,EACH,OAAO,CAAC,wBAAwB,CAAC,IAAI,CACtC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,YAAY,CAAC,GAAgB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvF,CAAC;IAED;;;OAGG;IACH,aAAa,CAAC,GAAgB;QAC5B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACzF,CAAC;IAED;;;OAGG;IACH,mBAAmB,CAAC,GAAgB;QAClC,OAAO,IAAI,CAAC,QAAQ,CAClB,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAClC,GAAG,EACH,OAAO,CAAC,sBAAsB,CAAC,IAAI,CACpC,CAAC;IACJ,CAAC;IAED,+CAA+C;IAC/C;;OAEG;IACK,QAAQ,CACd,GAAW,EACX,IAAiB,EACjB,IAAY;QAEZ,OAAO,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACxC,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,EAAE,CAAC;gBACrD,MAAM,CAAC,iBAAiB,CAAC,CAAC;gBAC1B,OAAO;YACT,CAAC;YACD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE,CAAC;gBACtD,MAAM,CAAC,iBAAiB,CAAC,CAAC;gBAC1B,OAAO;YACT,CAAC;YACD,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,GAAG,UAAU,CAAQ,CAAC;YACpE,MAAM,OAAO,GAAG,gBAAgB,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YAChF,IAAI,CAAC,OAAO;iBACT,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC;iBACtB,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjB,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;oBAClB,MAAM,iBAAiB,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,GAAG,WAAW,CAAQ,CAAC;oBACtE,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAC1C,MAAM,WAAW,GAAG,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBAClD,IACE,WAAW,CAAC,OAAO,IAAI,IAAI;wBAC3B,WAAW,CAAC,OAAO,IAAI,SAAS;wBAChC,WAAW,CAAC,OAAO,KAAK,CAAC,EACzB,CAAC;wBACD,OAAO,CAAC,WAAgB,CAAC,CAAC;oBAC5B,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,WAAW,CAAC,CAAC;oBACtB,CAAC;gBACH,CAAC;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI;QACF,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAEO,OAAO,CAAC,GAAW,EAAE,QAAa;QACxC,IAAI,QAAQ,IAAI,GAAG,EAAE,CAAC;YACpB,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBACrD,IAAI,QAAQ,CAAC,KAAK,KAAK,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;oBAC1C,MAAM,GAAG,GAAG,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;oBAC1C,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;wBAC5B,MAAM,uBAAuB,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,WAAW,CAAQ,CAAC;wBAChF,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;wBAC1C,MAAM,SAAS,GAAG,uBAAuB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;wBACtD,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;oBAC9B,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;CACF"}