{"version": 3, "file": "FutuWebSocket.js", "sourceRoot": "", "sources": ["../src/FutuWebSocket.ts"], "names": [], "mappings": "AAAA,OAAO,iBAAiB,MAAM,qBAAqB,CAAC;AACpD,OAAO,EAAE,OAAO,EAAE,MAAM,aAAa,CAAC;AACtC,OAAO,QAAQ,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AACpC,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,SAAS,MAAM,YAAY,CAAC;AAgBnC,cAAc;AACd,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AAC1B,QAAQ,CAAC,SAAS,EAAE,CAAC;AAErB;IAAA;QACU,YAAO,GAA6B,IAAI,CAAC;QAC1C,YAAO,GAAyB,IAAI,CAAC;QACrC,WAAM,GAAwB,IAAI,CAAC;IA6X5C,CAAC;IA3XQ,wBAAU,GAAjB,UAAkB,GAAW;QAC3B,KAAK,IAAM,GAAG,IAAI,OAAO,EAAE;YACzB,IAAM,OAAO,GAAG,OAAO,CAAC,GAA2B,CAAC,CAAC;YACrD,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,MAAK,GAAG,EAAE;gBACxB,OAAO,OAAO,CAAC;aAChB;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,6BAAK,GAAL,UAAM,EAAU,EAAE,IAAY,EAAE,GAAY,EAAE,GAAY;QAA1D,iBAgCC;QA/BC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE;YACrD,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAiB,EAAE,CAAC;YACvC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SAC7D;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;SACtB;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QAExC,IAAI,OAAO,GAAsB,IAAI,CAAC;QACtC,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,GAAG,EAAE;YACP,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACtD;QAED,IAAM,6BAA6B,GAAG,SAAS,CAAC,MAAM,CAAC,uBAAuB,CAAQ,CAAC;QACvF,IAAM,gBAAgB,GAAG;YACvB,GAAG,EAAE;gBACH,YAAY,EAAE,MAAM;gBACpB,mBAAmB,EAAE,YAAY;aAClC;SACF,CAAC;QACF,OAAO,GAAG,6BAA6B,CAAC,MAAM,CAC5C,6BAA6B,CAAC,MAAM,CAAC,gBAAgB,CAAC,CACvD,CAAC,MAAM,EAAE,CAAC;QACX,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,IAAI,SAAS,CAAC,CAAC;QAEjD,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,UAAC,GAAY,EAAE,GAAQ;YAC5C,IAAI,KAAI,CAAC,OAAO,IAAI,OAAO,KAAI,CAAC,OAAO,KAAK,UAAU,EAAE;gBACtD,KAAI,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aACxB;QACH,CAAC,CAAC;IACJ,CAAC;IAED,iCAAS,GAAT;;QACE,OAAO,CAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,KAAI,CAAC,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,2BAAG,GAAH,UAAI,GAAgB;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACrE,CAAC;IAED;;;OAGG;IACH,kCAAU,GAAV,UAAW,GAAsB;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACnF,CAAC;IAED;;;OAGG;IACH,mCAAW,GAAX,UAAY,GAAuB;QACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAED;;;OAGG;IACH,kCAAU,GAAV,UAAW,GAAgB;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACnF,CAAC;IAED;;;OAGG;IACH,gCAAQ,GAAR,UAAS,GAAoB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC/E,CAAC;IAED;;;OAGG;IACH,uCAAe,GAAf,UAAgB,GAA2B;QACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC7F,CAAC;IAED;;;OAGG;IACH,qCAAa,GAAb,UAAc,GAAgB;QAC5B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACzF,CAAC;IAED;;;OAGG;IACH,oCAAY,GAAZ,UAAa,GAAgB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvF,CAAC;IAED;;;OAGG;IACH,kCAAU,GAAV,UAAW,GAAsB;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACnF,CAAC;IAED;;;OAGG;IACH,mCAAW,GAAX,UAAY,GAAgB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAED;;;OAGG;IACH,wCAAgB,GAAhB,UAAiB,GAAgB;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC/F,CAAC;IAED;;;OAGG;IACH,2CAAmB,GAAnB,UAAoB,GAAgB;QAClC,OAAO,IAAI,CAAC,QAAQ,CAClB,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAClC,GAAG,EACH,OAAO,CAAC,sBAAsB,CAAC,IAAI,CACpC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,+CAAuB,GAAvB,UAAwB,GAAgB;QACtC,OAAO,IAAI,CAAC,QAAQ,CAClB,OAAO,CAAC,0BAA0B,CAAC,GAAG,EACtC,GAAG,EACH,OAAO,CAAC,0BAA0B,CAAC,IAAI,CACxC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,sCAAc,GAAd,UAAe,GAAgB;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC3F,CAAC;IAED;;;OAGG;IACH,mCAAW,GAAX,UAAY,GAAgB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAED;;;OAGG;IACH,sCAAc,GAAd,UAAe,GAAgB;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAED;;;OAGG;IACH,sCAAc,GAAd,UAAe,GAAgB;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAED;;;OAGG;IACH,kCAAU,GAAV,UAAW,GAAgB;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACnF,CAAC;IAED;;;OAGG;IACH,kCAAU,GAAV,UAAW,GAAgB;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACnF,CAAC;IAED;;;OAGG;IACH,iCAAS,GAAT,UAAU,GAAgB;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACjF,CAAC;IAED;;;OAGG;IACH,mCAAW,GAAX,UAAY,GAAgB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IAED;;;OAGG;IACH,oCAAY,GAAZ,UAAa,GAAgB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvF,CAAC;IAED;;;OAGG;IACH,6BAAK,GAAL,UAAM,GAAgB;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACzE,CAAC;IAED;;;OAGG;IACH,6BAAK,GAAL,UAAM,GAAgB;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACzE,CAAC;IAED;;;OAGG;IACH,iCAAS,GAAT,UAAU,GAAgB;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACjF,CAAC;IAED,gBAAgB;IAEhB;;;OAGG;IACH,oCAAY,GAAZ,UAAa,GAAgB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvF,CAAC;IAED;;;OAGG;IACH,wCAAgB,GAAhB,UAAiB,GAAgB;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC/F,CAAC;IAED;;;OAGG;IACH,6CAAqB,GAArB,UAAsB,GAAgB;QACpC,OAAO,IAAI,CAAC,QAAQ,CAClB,OAAO,CAAC,wBAAwB,CAAC,GAAG,EACpC,GAAG,EACH,OAAO,CAAC,wBAAwB,CAAC,IAAI,CACtC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,oCAAY,GAAZ,UAAa,GAAgB;QAC3B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvF,CAAC;IAED;;;OAGG;IACH,qCAAa,GAAb,UAAc,GAAgB;QAC5B,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACzF,CAAC;IAED;;;OAGG;IACH,2CAAmB,GAAnB,UAAoB,GAAgB;QAClC,OAAO,IAAI,CAAC,QAAQ,CAClB,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAClC,GAAG,EACH,OAAO,CAAC,sBAAsB,CAAC,IAAI,CACpC,CAAC;IACJ,CAAC;IAED,+CAA+C;IAC/C;;OAEG;IACK,gCAAQ,GAAhB,UACE,GAAW,EACX,IAAiB,EACjB,IAAY;QAHd,iBAsCC;QAjCC,OAAO,IAAI,OAAO,CAAI,UAAC,OAAO,EAAE,MAAM;YACpC,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,EAAE;gBACpD,MAAM,CAAC,iBAAiB,CAAC,CAAC;gBAC1B,OAAO;aACR;YACD,IAAI,KAAI,CAAC,OAAO,IAAI,IAAI,IAAI,KAAI,CAAC,OAAO,IAAI,SAAS,EAAE;gBACrD,MAAM,CAAC,iBAAiB,CAAC,CAAC;gBAC1B,OAAO;aACR;YACD,IAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,GAAG,UAAU,CAAQ,CAAC;YACpE,IAAM,OAAO,GAAG,gBAAgB,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YAChF,KAAI,CAAC,OAAO;iBACT,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC;iBACtB,IAAI,CAAC,UAAC,QAAQ;gBACb,IAAI,QAAQ,CAAC,IAAI,EAAE;oBACjB,IAAM,iBAAiB,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,GAAG,WAAW,CAAQ,CAAC;oBACtE,IAAM,GAAG,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAC1C,IAAM,WAAW,GAAG,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBAClD,IACE,WAAW,CAAC,OAAO,IAAI,IAAI;wBAC3B,WAAW,CAAC,OAAO,IAAI,SAAS;wBAChC,WAAW,CAAC,OAAO,KAAK,CAAC,EACzB;wBACA,OAAO,CAAC,WAAgB,CAAC,CAAC;qBAC3B;yBAAM;wBACL,MAAM,CAAC,WAAW,CAAC,CAAC;qBACrB;iBACF;YACH,CAAC,CAAC,CACD,OAAK,CAAA,CAAC,UAAC,KAAK;gBACX,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAED,4BAAI,GAAJ;QACE,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE;YACrD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;SACtC;IACH,CAAC;IAEO,+BAAO,GAAf,UAAgB,GAAW,EAAE,QAAa;QACxC,IAAI,QAAQ,IAAI,GAAG,EAAE;YACnB,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE;gBACpD,IAAI,QAAQ,CAAC,KAAK,KAAK,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE;oBACzC,IAAM,GAAG,GAAG,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;oBAC1C,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE;wBAC3B,IAAM,uBAAuB,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,WAAW,CAAQ,CAAC;wBAChF,IAAM,GAAG,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;wBAC1C,IAAM,SAAS,GAAG,uBAAuB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;wBACtD,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;qBAC7B;iBACF;aACF;SACF;IACH,CAAC;IACH,oBAAC;AAAD,CAAC,AAhYD,IAgYC"}