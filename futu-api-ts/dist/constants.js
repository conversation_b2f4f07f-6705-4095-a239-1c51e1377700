// 命令 ID 常量定义
export const ftCmdID = {
    // 全局协议
    InitConnect: { cmd: 1001, name: 'GetGlobalState', description: '初始化连接' },
    GetGlobalState: { cmd: 1002, name: 'GetGlobalState', description: '获取全局状态' },
    Notify: { cmd: 1003, name: 'Notify', description: '推送通知' },
    KeepAlive: { cmd: 1004, name: 'KeepAlive', description: '心跳' },
    GetUserInfo: { cmd: 1005, name: 'GetUserInfo', description: '获取用户信息' },
    GetDelayStatistics: { cmd: 1007, name: 'GetDelayStatistics', description: '获取延迟统计' },
    // 行情-实时数据协议
    QotSub: { cmd: 3001, name: 'Qot_Sub', description: '订阅或者反订阅' },
    QotRegQotPush: { cmd: 3002, name: 'Qot_RegQotPush', description: '注册推送' },
    QotGetSubInfo: { cmd: 3003, name: 'Qot_GetSubInfo', description: '获取订阅信息' },
    QotGetBasicQot: { cmd: 3004, name: 'Qot_GetBasicQot', description: '获取基本行情' },
    QotUpdateBasicQot: { cmd: 3005, name: 'Qot_UpdateBasicQot', description: '推送基本行情' },
    QotGetKL: { cmd: 3006, name: 'Qot_GetKL', description: '获取K线' },
    QotUpdateKL: { cmd: 3007, name: 'Qot_UpdateKL', description: '推送K线' },
    QotGetRT: { cmd: 3008, name: 'Qot_GetRT', description: '获取分时' },
    QotUpdateRT: { cmd: 3009, name: 'Qot_UpdateRT', description: '获取分时' },
    QotGetTicker: { cmd: 3010, name: 'Qot_GetTicker', description: '获取逐笔' },
    QotUpdateTicker: { cmd: 3011, name: 'Qot_UpdateTicker', description: '推送逐笔' },
    QotGetOrderBook: { cmd: 3012, name: 'Qot_GetOrderBook', description: '获取买卖盘' },
    QotUpdateOrderBook: { cmd: 3013, name: 'Qot_UpdateOrderBook', description: '推送买卖盘' },
    QotGetBroker: { cmd: 3014, name: 'Qot_GetBroker', description: '获取经纪队列' },
    QotUpdateBroker: { cmd: 3015, name: 'Qot_UpdateBroker', description: '推送经纪队列' },
    QotUpdatePriceReminder: {
        cmd: 3019,
        name: 'Qot_UpdatePriceReminder',
        description: '推送到价提醒',
    },
    // 行情-历史数据协议
    QotGetHistoryKL: { cmd: 3100, name: 'Qot_GetHistoryKL', description: '获取历史K线' },
    QotGetHistoryKLPoints: {
        cmd: 3101,
        name: 'Qot_GetHistoryKLPoints',
        description: '获取多只股票历史单点K线',
    },
    QotGetRehab: { cmd: 3102, name: 'Qot_GetRehab', description: '获取复权信息' },
    QotRequestHistoryKL: {
        cmd: 3103,
        name: 'Qot_RequestHistoryKL',
        description: '拉取历史K线，不读本地历史数据DB',
    },
    QotRequestHistoryKLQuota: {
        cmd: 3104,
        name: 'Qot_RequestHistoryKLQuota',
        description: '拉取历史K线已经用掉的额度',
    },
    QotRequestRehab: {
        cmd: 3105,
        name: 'Qot_RequestRehab',
        description: '拉取复权信息，不读本地历史数据DB',
    },
    // 行情-其他数据协议
    QotGetTradeDate: { cmd: 3200, name: 'Qot_GetTradeDate', description: '获取市场交易日' },
    QotGetSuspend: { cmd: 3201, name: 'Qot_GetSuspend', description: '获取股票停牌信息' },
    QotGetStaticInfo: { cmd: 3202, name: 'Qot_GetStaticInfo', description: '获取股票静态信息' },
    QotGetSecuritySnapshot: {
        cmd: 3203,
        name: 'Qot_GetSecuritySnapshot',
        description: '获取股票快照',
    },
    QotGetPlateSet: { cmd: 3204, name: 'Qot_GetPlateSet', description: '获取板块集合下的板块' },
    QotGetPlateSecurity: { cmd: 3205, name: 'Qot_GetPlateSecurity', description: '获取板块下的股票' },
    QotGetReference: {
        cmd: 3206,
        name: 'Qot_GetReference',
        description: '获取正股相关股票，暂时只有窝轮',
    },
    QotGetOwnerPlate: { cmd: 3207, name: 'Qot_GetOwnerPlate', description: '获取股票所属板块' },
    QotGetHoldingChangeList: {
        cmd: 3208,
        name: 'Qot_GetHoldingChangeList',
        description: '获取大股东持股变化列表',
    },
    QotGetOptionChain: { cmd: 3209, name: 'Qot_GetOptionChain', description: '获取期权链' },
    QotGetWarrant: { cmd: 3210, name: 'Qot_GetWarrant', description: '获取涡轮' },
    QotGetCapitalFlow: { cmd: 3211, name: 'Qot_GetCapitalFlow', description: '获取资金流向' },
    QotGetCapitalDistribution: {
        cmd: 3212,
        name: 'Qot_GetCapitalDistribution',
        description: '获取资金分布',
    },
    QotGetUserSecurity: {
        cmd: 3213,
        name: 'Qot_GetUserSecurity',
        description: '获取自选股分组下的股票',
    },
    QotModifyUserSecurity: {
        cmd: 3214,
        name: 'Qot_ModifyUserSecurity',
        description: '修改自选股分组下的股票',
    },
    QotStockFilter: { cmd: 3215, name: 'Qot_StockFilter', description: '条件选股' },
    QotGetCodeChange: { cmd: 3216, name: 'Qot_GetCodeChange', description: '获取股票代码变化信息' },
    QotGetIpoList: { cmd: 3217, name: 'Qot_GetIpoList', description: '获取新股IPO' },
    QotGetFutureInfo: { cmd: 3218, name: 'Qot_GetFutureInfo', description: '获取期货合约资料' },
    QotRequestTradeDate: { cmd: 3219, name: 'Qot_RequestTradeDate', description: '获取市场交易日' },
    QotSetPriceReminder: { cmd: 3220, name: 'Qot_SetPriceReminder', description: '设置到价提醒' },
    QotGetPriceReminder: { cmd: 3221, name: 'Qot_GetPriceReminder', description: '获取到价提醒' },
    QotGetUserSecurityGroup: {
        cmd: 3222,
        name: 'Qot_GetUserSecurityGroup',
        description: '获取自选股分组列表',
    },
    QotGetMarketState: { cmd: 3223, name: 'Qot_GetMarketState', description: '获取股票对应市场状态' },
    QotGetOptionExpirationDate: {
        cmd: 3224,
        name: 'Qot_GetOptionExpirationDate',
        description: '获取期权链到期日',
    },
    // 交易协议
    TrdGetAccList: { cmd: 2001, name: 'Trd_GetAccList', description: '获取交易账户列表' },
    TrdUnlockTrade: { cmd: 2005, name: 'Trd_UnlockTrade', description: '解锁或锁定交易' },
    TrdSubAccPush: { cmd: 2008, name: 'Trd_SubAccPush', description: '订阅接收推送数据的交易账户' },
    TrdGetFunds: { cmd: 2101, name: 'Trd_GetFunds', description: '获取账户资金' },
    TrdGetPositionList: { cmd: 2102, name: 'Trd_GetPositionList', description: '获取账户持仓' },
    TrdGetMaxTrdQtys: { cmd: 2111, name: 'Trd_GetMaxTrdQtys', description: '获取最大交易数量' },
    TrdGetOrderList: { cmd: 2201, name: 'Trd_GetOrderList', description: '获取订单列表' },
    TrdPlaceOrder: { cmd: 2202, name: 'Trd_PlaceOrder', description: '下单' },
    TrdModifyOrder: { cmd: 2205, name: 'Trd_ModifyOrder', description: '修改订单' },
    TrdUpdateOrder: { cmd: 2208, name: 'Trd_UpdateOrder', description: '订单状态变动通知(推送)' },
    TrdGetOrderFillList: { cmd: 2211, name: 'Trd_GetOrderFillList', description: '获取成交列表' },
    TrdUpdateOrderFill: { cmd: 2218, name: 'Trd_UpdateOrderFill', description: '成交通知(推送)' },
    TrdGetHistoryOrderList: {
        cmd: 2221,
        name: 'Trd_GetHistoryOrderList',
        description: '获取历史订单列表',
    },
    TrdGetHistoryOrderFillList: {
        cmd: 2222,
        name: 'Trd_GetHistoryOrderFillList',
        description: '获取历史成交列表',
    },
    TrdGetMarginRatio: { cmd: 2223, name: 'Trd_GetMarginRatio', description: '获取融资融券数据' },
    TrdGetOrderFee: { cmd: 2225, name: 'Trd_GetOrderFee', description: '获取订单收费明细数据' },
    TrdFlowSummary: { cmd: 2226, name: 'Trd_FlowSummary', description: '获取资金流水' },
};
//# sourceMappingURL=constants.js.map