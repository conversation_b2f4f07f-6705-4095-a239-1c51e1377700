/**
 * @fileOverview 富途OpenD websocket TypeScript接口封装
 * <AUTHOR>
 * @version 1.0
 */
import type { ResponseData, LoginCallback, PushCallback, MessageCallback, ErrorCallback, CloseCallback } from './types/base.js';
/**
 * @name FutuWebSocketBase
 * @class websocket封装工具类
 * @constructor FutuWebSocketBase
 * @classdesc websocket封装工具类
 */
export default class FutuWebSocketBase {
    private websock;
    private wsuri;
    private initOpenDConfigBuff;
    private timeout;
    private reconnectTimeout;
    private promisePool;
    private state;
    private pushCalls;
    private reconnectTimer;
    connID: number;
    onlogin?: LoginCallback;
    onmessage?: MessageCallback;
    onerror?: ErrorCallback;
    onclose?: CloseCallback;
    /**
     * 初始化设置websocket连接的ip和端口号
     * @param ip ip地址，如127.0.0.1
     * @param port 端口号，如 8080
     * @param ssl 是否使用SSL
     */
    setWsConfig(ip: string, port: number, ssl: boolean): void;
    private packBuff;
    private unpackBuff;
    /**
     * 数据发送，适用于一应一答的场景，发送数据后，必然回包或超时
     * @param cmd 命令字，如1001
     * @param buff 发送的二进制数据，Uint8Array或者ArrayBuffer格式
     * @param timeout 协议超时时间，默认5s超时
     * @returns Promise 返回响应数据或错误
     */
    sendBuff(cmd: number, buff?: Uint8Array | ArrayBuffer, timeout?: number): Promise<ResponseData>;
    private recvBuff;
    /**
     * 注册push回调，回调链可以注册多个，务必需要反注册
     * @param key 用于反注册时候使用的key
     * @param func 回调函数，需要满足接收一个参数，参数是传回的整个包
     */
    regPushCallback(key: any, func: PushCallback): void;
    /**
     * 反注册push回调
     * @param key 用于反注册时候使用的key
     */
    unregPushCallback(key: any): void;
    /**
     * 关闭socket
     */
    close(): void;
    private rejectAll;
    private reconnect;
    isReadyConnect(): boolean;
    private killReconnectTimer;
    /**
     * 初始化websocket
     * @param configBuffer 参考InitWebSocket.proto，非必填字段
     */
    initWebSocket(configBuffer?: Uint8Array): this;
}
//# sourceMappingURL=FutuWebSocketBase.d.ts.map