{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../src/constants.ts"], "names": [], "mappings": "AAAA,aAAa;AACb,MAAM,CAAC,MAAM,OAAO,GAAG;IACrB,OAAO;IACP,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,OAAO,EAAE;IACxE,cAAc,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE;IAC5E,MAAM,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE;IAC1D,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE;IAC9D,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE;IACtE,kBAAkB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,oBAAoB,EAAE,WAAW,EAAE,QAAQ,EAAE;IAEpF,YAAY;IACZ,MAAM,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE;IAC9D,aAAa,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,EAAE;IACzE,aAAa,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE;IAC3E,cAAc,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,QAAQ,EAAE;IAC7E,iBAAiB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,oBAAoB,EAAE,WAAW,EAAE,QAAQ,EAAE;IACnF,QAAQ,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE;IAC/D,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,WAAW,EAAE,MAAM,EAAE;IACrE,QAAQ,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE;IAC/D,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,WAAW,EAAE,MAAM,EAAE;IACrE,YAAY,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,EAAE,MAAM,EAAE;IACvE,eAAe,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,WAAW,EAAE,MAAM,EAAE;IAC7E,eAAe,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,WAAW,EAAE,OAAO,EAAE;IAC9E,kBAAkB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,qBAAqB,EAAE,WAAW,EAAE,OAAO,EAAE;IACpF,YAAY,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,EAAE,QAAQ,EAAE;IACzE,eAAe,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,WAAW,EAAE,QAAQ,EAAE;IAC/E,sBAAsB,EAAE;QACtB,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,yBAAyB;QAC/B,WAAW,EAAE,QAAQ;KACtB;IAED,YAAY;IACZ,eAAe,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,WAAW,EAAE,QAAQ,EAAE;IAC/E,qBAAqB,EAAE;QACrB,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,wBAAwB;QAC9B,WAAW,EAAE,cAAc;KAC5B;IACD,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,WAAW,EAAE,QAAQ,EAAE;IACvE,mBAAmB,EAAE;QACnB,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,sBAAsB;QAC5B,WAAW,EAAE,mBAAmB;KACjC;IACD,wBAAwB,EAAE;QACxB,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,2BAA2B;QACjC,WAAW,EAAE,eAAe;KAC7B;IACD,eAAe,EAAE;QACf,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,kBAAkB;QACxB,WAAW,EAAE,mBAAmB;KACjC;IAED,YAAY;IACZ,eAAe,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,WAAW,EAAE,SAAS,EAAE;IAChF,aAAa,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,EAAE;IAC7E,gBAAgB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,mBAAmB,EAAE,WAAW,EAAE,UAAU,EAAE;IACnF,sBAAsB,EAAE;QACtB,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,yBAAyB;QAC/B,WAAW,EAAE,QAAQ;KACtB;IACD,cAAc,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,YAAY,EAAE;IACjF,mBAAmB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,sBAAsB,EAAE,WAAW,EAAE,UAAU,EAAE;IACzF,eAAe,EAAE;QACf,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,kBAAkB;QACxB,WAAW,EAAE,iBAAiB;KAC/B;IACD,gBAAgB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,mBAAmB,EAAE,WAAW,EAAE,UAAU,EAAE;IACnF,uBAAuB,EAAE;QACvB,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,0BAA0B;QAChC,WAAW,EAAE,aAAa;KAC3B;IACD,iBAAiB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,oBAAoB,EAAE,WAAW,EAAE,OAAO,EAAE;IAClF,aAAa,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,EAAE;IACzE,iBAAiB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,oBAAoB,EAAE,WAAW,EAAE,QAAQ,EAAE;IACnF,yBAAyB,EAAE;QACzB,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,4BAA4B;QAClC,WAAW,EAAE,QAAQ;KACtB;IACD,kBAAkB,EAAE;QAClB,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,qBAAqB;QAC3B,WAAW,EAAE,aAAa;KAC3B;IACD,qBAAqB,EAAE;QACrB,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,wBAAwB;QAC9B,WAAW,EAAE,aAAa;KAC3B;IACD,cAAc,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,MAAM,EAAE;IAC3E,gBAAgB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,mBAAmB,EAAE,WAAW,EAAE,YAAY,EAAE;IACrF,aAAa,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,EAAE;IAC5E,gBAAgB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,mBAAmB,EAAE,WAAW,EAAE,UAAU,EAAE;IACnF,mBAAmB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,sBAAsB,EAAE,WAAW,EAAE,SAAS,EAAE;IACxF,mBAAmB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,sBAAsB,EAAE,WAAW,EAAE,QAAQ,EAAE;IACvF,mBAAmB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,sBAAsB,EAAE,WAAW,EAAE,QAAQ,EAAE;IACvF,uBAAuB,EAAE;QACvB,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,0BAA0B;QAChC,WAAW,EAAE,WAAW;KACzB;IACD,iBAAiB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,oBAAoB,EAAE,WAAW,EAAE,YAAY,EAAE;IACvF,0BAA0B,EAAE;QAC1B,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,6BAA6B;QACnC,WAAW,EAAE,UAAU;KACxB;IAED,OAAO;IACP,aAAa,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,EAAE;IAC7E,cAAc,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,SAAS,EAAE;IAC9E,aAAa,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,eAAe,EAAE;IAClF,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,WAAW,EAAE,QAAQ,EAAE;IACvE,kBAAkB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,qBAAqB,EAAE,WAAW,EAAE,QAAQ,EAAE;IACrF,gBAAgB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,mBAAmB,EAAE,WAAW,EAAE,UAAU,EAAE;IACnF,eAAe,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,WAAW,EAAE,QAAQ,EAAE;IAC/E,aAAa,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,IAAI,EAAE;IACvE,cAAc,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,MAAM,EAAE;IAC3E,cAAc,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,cAAc,EAAE;IACnF,mBAAmB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,sBAAsB,EAAE,WAAW,EAAE,QAAQ,EAAE;IACvF,kBAAkB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,qBAAqB,EAAE,WAAW,EAAE,UAAU,EAAE;IACvF,sBAAsB,EAAE;QACtB,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,yBAAyB;QAC/B,WAAW,EAAE,UAAU;KACxB;IACD,0BAA0B,EAAE;QAC1B,GAAG,EAAE,IAAI;QACT,IAAI,EAAE,6BAA6B;QACnC,WAAW,EAAE,UAAU;KACxB;IACD,iBAAiB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,oBAAoB,EAAE,WAAW,EAAE,UAAU,EAAE;IACrF,cAAc,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,YAAY,EAAE;IACjF,cAAc,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,QAAQ,EAAE;CACrE,CAAC"}