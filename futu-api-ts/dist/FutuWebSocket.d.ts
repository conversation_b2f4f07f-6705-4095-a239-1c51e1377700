import type { BaseRequest, BaseResponse, GetAccListRequest, GetAccListResponse, UnlockTradeRequest, GetFundsRequest, GetFundsResponse, GetPositionListRequest, GetPositionListResponse, PlaceOrderRequest, PlaceOrderResponse } from './types/api.js';
import type { LoginCallback, PushCallback } from './types/base.js';
export default class FutuWebSocket {
    private websock;
    onlogin: LoginCallback | null;
    onPush: PushCallback | null;
    static findCmdObj(cmd: number): {
        cmd: number;
        name: string;
        description: string;
    } | null;
    start(ip: string, port: number, ssl: boolean, key?: string): void;
    getConnID(): number;
    /**
     * 订阅，反订阅
     * @param req 请求包，具体字段请参考Qot_Sub.proto协议
     */
    Sub(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取交易帐号列表
     * @param req 请求包，具体字段请参考Trd_GetAccList.proto协议
     */
    GetAccList(req: GetAccListRequest): Promise<GetAccListResponse>;
    /**
     * 解锁，针对OpenD解锁一次即可
     * @param req 请求包，具体字段请参考Trd_UnlockTrade.proto协议
     */
    UnlockTrade(req: UnlockTradeRequest): Promise<BaseResponse>;
    /**
     * 订阅接收推送数据的交易账户
     * @param req 请求包，具体字段请参考Trd_SubAccPush.proto协议
     */
    SubAccPush(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取账户资金
     * @param req 请求包，具体字段请参考Trd_GetFunds.proto协议
     */
    GetFunds(req: GetFundsRequest): Promise<GetFundsResponse>;
    /**
     * 获取账户持仓
     * @param req 请求包，具体字段请参考Trd_GetPositionList.proto协议
     */
    GetPositionList(req: GetPositionListRequest): Promise<GetPositionListResponse>;
    /**
     * 获取最大交易数量
     * @param req 请求包，具体字段请参考Trd_GetMaxTrdQtys.proto协议
     */
    GetMaxTrdQtys(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取当日订单列表
     * @param req 请求包，具体字段请参考Trd_GetOrderList.proto协议
     */
    GetOrderList(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 下单
     * @param req 请求包，具体字段请参考Trd_PlaceOrder.proto协议，PacketID不需填写，发送时接口会填
     */
    PlaceOrder(req: PlaceOrderRequest): Promise<PlaceOrderResponse>;
    /**
     * 修改订单
     * @param req 请求包，具体字段请参考Trd_ModifyOrder.proto协议，PacketID不需填写，发送时接口会填
     */
    ModifyOrder(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取当日成交列表
     * @param req 请求包，具体字段请参考Trd_GetOrderFillList.proto协议
     */
    GetOrderFillList(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取历史订单列表
     * @param req 请求包，具体字段请参考Trd_GetHistoryOrderList.proto协议
     */
    GetHistoryOrderList(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取历史成交列表
     * @param req 请求包，具体字段请参考Trd_GetHistoryOrderFillList.proto协议
     */
    GetHistoryOrderFillList(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取融资融券数据
     * @param req 请求包，具体字段请参考Trd_GetMarginRatio.proto协议
     */
    GetMarginRatio(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取订单收费明细数据
     * @param req 请求包，具体字段请参考Trd_GetOrderFee.proto协议
     */
    GetOrderFee(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取资金流水
     * @param req 请求包，具体字段请参考Trd_FlowSummary.proto协议
     */
    GetFlowSummary(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 请求全局状态
     * @param req 具体字段请参考GetGlobalState.proto协议
     */
    GetGlobalState(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 注册推送
     * @param req 具体字段请参考Qot_RegQotPush.proto协议
     */
    RegQotPush(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取订阅信息
     * @param req 具体字段请参考Qot_GetSubInfo.proto协议
     */
    GetSubInfo(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取逐笔
     * @param req 具体字段请参考Qot_GetTicker.proto协议
     */
    GetTicker(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取报价
     * @param req 具体字段请参考Qot_GetBasicQot.proto协议
     */
    GetBasicQot(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取摆盘
     * @param req 具体字段请参考Qot_GetOrderBook.proto协议
     */
    GetOrderBook(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取K线
     * @param req 具体字段请参考Qot_GetKL.proto协议
     */
    GetKL(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取分时
     * @param req 具体字段请参考Qot_GetRT.proto协议
     */
    GetRT(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取经纪队列
     * @param req 具体字段请参考Qot_GetBroker.proto协议
     */
    GetBroker(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 在线请求历史复权信息，不读本地历史数据DB
     * @param req 具体字段请参考Qot_RequestRehab.proto协议
     */
    RequestRehab(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 在线请求历史K线，不读本地历史数据DB
     * @param req 具体字段请参考Qot_RequestHistoryKL.proto协议
     */
    RequestHistoryKL(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取历史K线已经用掉的额度
     * @param req 具体字段请参考Qot_RequestHistoryKLQuota.proto协议
     */
    RequestHistoryKLQuota(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取交易日
     * @param req 具体字段请参考Qot_GetTradeDate.proto协议
     */
    GetTradeDate(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取静态信息
     * @param req 具体字段请参考Qot_GetStaticInfo.proto协议
     */
    GetStaticInfo(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 获取股票快照
     * @param req 具体字段请参考Qot_GetSecuritySnapshot.proto协议
     */
    GetSecuritySnapshot(req: BaseRequest): Promise<BaseResponse>;
    /**
     * 发送数据
     */
    private _sendCmd;
    stop(): void;
    private _onPush;
}
//# sourceMappingURL=FutuWebSocket.d.ts.map