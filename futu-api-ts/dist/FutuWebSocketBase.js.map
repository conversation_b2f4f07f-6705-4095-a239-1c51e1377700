{"version": 3, "file": "FutuWebSocketBase.js", "sourceRoot": "", "sources": ["../src/FutuWebSocketBase.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,MAAM,CAAC;AAC9B,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,SAAS,MAAM,YAAY,CAAC;AAanC,MAAM,UAAU,GAAG;IACjB,IAAI,EAAE,CAAU;IAChB,cAAc,EAAE,CAAU;CAC3B,CAAC;AAEF,MAAM,oBAAoB,GAAG;IAC3B,SAAS,EAAE,CAAU;CACtB,CAAC;AAEF,6BAA6B;AAC7B,yCAAyC;AACzC,+CAA+C;AAC/C,4CAA4C;AAC5C,KAAK;AAEL,IAAI,kBAAkB,GAAG,CAAC,CAAC;AAC3B,MAAM,qBAAqB,GAAG,EAAE,CAAC;AACjC,MAAM,mBAAmB,GAAG,SAAS,CAAC;AAEtC;;;;;GAKG;AACH,MAAM,CAAC,OAAO,OAAO,iBAAiB;IAC5B,OAAO,GAAqB,IAAI,CAAC;IACjC,KAAK,GAAG,sBAAsB,CAAC;IAC/B,mBAAmB,GAAsB,IAAI,CAAC;IAC9C,OAAO,GAAG,IAAI,CAAC,CAAC,KAAK;IACrB,gBAAgB,GAAG,IAAI,CAAC,CAAC,SAAS;IAClC,WAAW,GAAoC,EAAE,CAAC;IAClD,KAAK,GAAmB;QAC9B,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,KAAK;KACb,CAAC;IACM,SAAS,GAAG,IAAI,GAAG,EAAqB,CAAC;IACzC,cAAc,GAA0B,IAAI,CAAC;IAC9C,MAAM,GAAG,CAAC,CAAC;IAElB,OAAO;IACA,OAAO,CAAiB;IACxB,SAAS,CAAmB;IAC5B,OAAO,CAAiB;IACxB,OAAO,CAAiB;IAE/B;;;;;OAKG;IACH,WAAW,CAAC,EAAU,EAAE,IAAY,EAAE,GAAY;QAChD,IAAI,EAAE,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YACjC,IAAI,KAAa,CAAC;YAClB,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;gBAClB,KAAK,GAAG,MAAM,CAAC,YAAY,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,MAAM,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;YAC1C,CAAC;YACD,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;gBACzB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YACrB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,QAAQ,CAAC,GAAW,EAAE,OAAe,EAAE,IAA+B;QAC5E,MAAM,KAAK,GAAG,IAAI,UAAU,EAAE,CAAC;QAC/B,KAAK,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QAC3C,MAAM,cAAc,GAAG,CAAC,GAAG,UAAU,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QAC3E,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC;QACH,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACvB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC3B,IAAI,IAAI,YAAY,UAAU,EAAE,CAAC;YAC/B,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;YACvC,KAAK,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;QACrC,CAAC;QACD,KAAK,CAAC,IAAI,EAAE,CAAC;QACb,OAAO,KAAK,CAAC,aAAa,EAAE,CAAC;IAC/B,CAAC;IAED,OAAO;IACC,UAAU,CAAC,IAAiB;QAClC,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;YAChC,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,MAAM,GAAiB,EAAkB,CAAC;YAChD,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACnB,KAAK,CAAC,IAAI,EAAE,CAAC;YACb,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YAChC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC/C,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;YACjC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAC5D,IAAI,IAAI,CAAC,UAAU,GAAG,qBAAqB,EAAE,CAAC;gBAC5C,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,GAAG,qBAAqB,CAAC,CAAC;gBACtE,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACrC,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,QAAQ,CAAC,GAAW,EAAE,IAA+B,EAAE,OAAgB;QACrE,MAAM,IAAI,GAAG,IAAI,CAAC;QAElB,OAAO;QACP,SAAS,eAAe,CAAC,OAAe,EAAE,SAAiB;YACzD,OAAO,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;gBACtC,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC5B,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;wBACtC,MAAM,CAAC,SAAS,CAAC,CAAC;wBAClB,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;wBAC7C,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC,EAAE,SAAS,CAAC,CAAC;gBACd,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC9B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;gBAC1C,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,aAAa;QACb,SAAS,eAAe,CAAC,OAAe;YACtC,OAAO,IAAI,OAAO,CAAe,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACnD,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC9B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;oBAC5C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;oBAC5C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;oBAC1C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,OAAQ,CAAC;gBACnD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YAC/D,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,UAAU,CAAC,IAAI,EAAE,CAAC;gBAChD,OAAO,GAAG,EAAE,kBAAkB,CAAC;gBAC/B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG;oBAC1B,OAAO,EAAE,CAAC;oBACV,OAAO,EAAE,GAAG,EAAE,GAAE,CAAC;oBACjB,MAAM,EAAE,GAAG,EAAE,GAAE,CAAC;oBAChB,MAAM,EAAE,IAAI,CAAC,OAAQ;iBACtB,CAAC;gBACF,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;gBACpD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7B,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;oBAC7C,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;gBACzB,CAAC;gBACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;YACrF,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;IACnD,CAAC;IAEO,QAAQ,CAAC,IAAiB;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACzE,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACpD,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;gBACpD,KAAK,GAAG,oBAAoB,CAAC,SAAS,CAAC;gBACvC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;YAC3B,CAAC;YAED,OAAQ,MAAc,CAAC,IAAI,CAAC;YAC5B,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;YAC/B,OAAQ,MAAc,CAAC,OAAO,CAAC;YAE/B,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACtC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;gBAChB,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACpB,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACjC,IAAI,GAAG,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;oBACtB,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBACxB,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC;gBACxB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;oBAC3B,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QACD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACH,eAAe,CAAC,GAAQ,EAAE,IAAkB;QAC1C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,iBAAiB,CAAC,GAAQ;QACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;QACD,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAEO,SAAS;QACf,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YAChE,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;gBAClD,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;oBAChB,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;wBACvB,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACtB,CAAC;oBACD,IAAI,GAAG,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;wBACtB,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBACxB,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC;oBACxB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IACxB,CAAC;IAEO,SAAS,CAAC,OAAe;QAC/B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE;YACpC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;gBACvE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACpB,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC7B,CAAC;QACH,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YACvE,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAEO,kBAAkB;QACxB,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;YACtE,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,aAAa,CAAC,YAAyB;QACrC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YACzB,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YAC5B,OAAQ,IAAY,CAAC,OAAO,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;QAC3B,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,YAAY,UAAU,EAAE,CAAC;YAC9F,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC;QAC1C,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,aAAa,CAAC;QAExC,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,EAAE;YAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,UAAU,EAAE,CAAC;gBACzE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACtB,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,EAAE;YACzB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,IAAI,SAAS,EAAE,KAAK,CAAC;iBACzE,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjB,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;oBAClD,WAAW;oBACX,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,wBAAwB,CAAQ,CAAC;oBACnE,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAC1C,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBACxC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC;oBACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBACtB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;wBACxB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;wBACxB,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;4BACrE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;wBAC/B,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;wBACrE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACtC,CAAC;oBACD,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;oBACzB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,CAAC;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACf,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;gBACrC,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;oBACrE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC7B,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE;YAC3B,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACzB,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;gBACrE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE;YAC3B,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACxC,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;YAEzB,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;gBACrE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,IAAI,CAAC;IACd,CAAC;CACF"}