{"version": 3, "file": "FutuWebSocketBase.js", "sourceRoot": "", "sources": ["../src/FutuWebSocketBase.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,MAAM,CAAC;AAC9B,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,SAAS,MAAM,YAAY,CAAC;AAanC,IAAM,UAAU,GAAG;IACjB,IAAI,EAAE,CAAU;IAChB,cAAc,EAAE,CAAU;CAC3B,CAAC;AAEF,IAAM,oBAAoB,GAAG;IAC3B,SAAS,EAAE,CAAU;CACtB,CAAC;AAEF,6BAA6B;AAC7B,yCAAyC;AACzC,+CAA+C;AAC/C,4CAA4C;AAC5C,KAAK;AAEL,IAAI,kBAAkB,GAAG,CAAC,CAAC;AAC3B,IAAM,qBAAqB,GAAG,EAAE,CAAC;AACjC,IAAM,mBAAmB,GAAG,SAAS,CAAC;AAEtC;;;;;GAKG;AACH;IAAA;QACU,YAAO,GAAqB,IAAI,CAAC;QACjC,UAAK,GAAG,sBAAsB,CAAC;QAC/B,wBAAmB,GAAsB,IAAI,CAAC;QAC9C,YAAO,GAAG,IAAI,CAAC,CAAC,KAAK;QACrB,qBAAgB,GAAG,IAAI,CAAC,CAAC,SAAS;QAClC,gBAAW,GAAoC,EAAE,CAAC;QAClD,UAAK,GAAmB;YAC9B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,KAAK;SACb,CAAC;QACM,cAAS,GAAG,IAAI,GAAG,EAAqB,CAAC;QACzC,mBAAc,GAA0B,IAAI,CAAC;QAC9C,WAAM,GAAG,CAAC,CAAC;IA4TpB,CAAC;IApTC;;;;;OAKG;IACH,uCAAW,GAAX,UAAY,EAAU,EAAE,IAAY,EAAE,GAAY;QAChD,IAAI,EAAE,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;YAChC,IAAI,KAAK,SAAQ,CAAC;YAClB,IAAI,GAAG,KAAK,KAAK,EAAE;gBACjB,KAAK,GAAG,MAAM,CAAC,YAAY,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;aACxC;iBAAM;gBACL,KAAK,GAAG,MAAM,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;aACzC;YACD,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;gBACxB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACpB;SACF;IACH,CAAC;IAEO,oCAAQ,GAAhB,UAAiB,GAAW,EAAE,OAAe,EAAE,IAA+B;QAC5E,IAAM,KAAK,GAAG,IAAI,UAAU,EAAE,CAAC;QAC/B,KAAK,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QAC3C,IAAM,cAAc,GAAG,CAAC,GAAG,UAAU,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QAC3E,IAAI,cAAc,GAAG,CAAC,EAAE;YACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;gBACvC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;aACpB;SACF;QACD,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACvB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC3B,IAAI,IAAI,YAAY,UAAU,EAAE;YAC9B,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACpB;aAAM,IAAI,IAAI,YAAY,WAAW,EAAE;YACtC,KAAK,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;SACpC;QACD,KAAK,CAAC,IAAI,EAAE,CAAC;QACb,OAAO,KAAK,CAAC,aAAa,EAAE,CAAC;IAC/B,CAAC;IAED,OAAO;IACC,sCAAU,GAAlB,UAAmB,IAAiB;QAClC,IAAI,IAAI,YAAY,WAAW,EAAE;YAC/B,IAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACrD,IAAM,MAAM,GAAiB,EAAkB,CAAC;YAChD,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACnB,KAAK,CAAC,IAAI,EAAE,CAAC;YACb,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YAChC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC/C,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;YACjC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAC5D,IAAI,IAAI,CAAC,UAAU,GAAG,qBAAqB,EAAE;gBAC3C,IAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,GAAG,qBAAqB,CAAC,CAAC;gBACtE,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;aACpC;YACD,OAAO,MAAM,CAAC;SACf;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,oCAAQ,GAAR,UAAS,GAAW,EAAE,IAA+B,EAAE,OAAgB;QACrE,IAAM,IAAI,GAAG,IAAI,CAAC;QAElB,OAAO;QACP,SAAS,eAAe,CAAC,OAAe,EAAE,SAAiB;YACzD,OAAO,IAAI,OAAO,CAAQ,UAAC,CAAC,EAAE,MAAM;gBAClC,IAAM,KAAK,GAAG,UAAU,CAAC;oBACvB,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE;wBACrC,MAAM,CAAC,SAAS,CAAC,CAAC;wBAClB,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;wBAC7C,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;qBAClC;gBACH,CAAC,EAAE,SAAS,CAAC,CAAC;gBACd,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;oBAC7B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;iBACzC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,aAAa;QACb,SAAS,eAAe,CAAC,OAAe;YACtC,OAAO,IAAI,OAAO,CAAe,UAAC,OAAO,EAAE,MAAM;gBAC/C,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;oBAC7B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;oBAC5C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;oBAC5C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;oBAC1C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,OAAQ,CAAC;iBAClD;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE;YAC9D,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,UAAU,CAAC,IAAI,EAAE;gBAC/C,OAAO,GAAG,EAAE,kBAAkB,CAAC;gBAC/B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG;oBAC1B,OAAO,EAAE,CAAC;oBACV,OAAO,EAAE,cAAO,CAAC;oBACjB,MAAM,EAAE,cAAO,CAAC;oBAChB,MAAM,EAAE,IAAI,CAAC,OAAQ;iBACtB,CAAC;gBACF,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;gBACpD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7B,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE;oBAC5C,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;iBACxB;gBACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;aACpF;SACF;QACD,OAAO,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;IACnD,CAAC;IAEO,oCAAQ,GAAhB,UAAiB,IAAiB;QAChC,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE;YACxE,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE;gBACnD,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;gBACpD,KAAK,GAAG,oBAAoB,CAAC,SAAS,CAAC;gBACvC,OAAO,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAC;aAC1B;YAED,OAAQ,MAAc,CAAC,IAAI,CAAC;YAC5B,IAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;YAC/B,OAAQ,MAAc,CAAC,OAAO,CAAC;YAE/B,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACtC,IAAI,GAAG,IAAI,IAAI,EAAE;gBACf,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACpB,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACjC,IAAI,GAAG,CAAC,KAAK,IAAI,IAAI,EAAE;oBACrB,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBACxB,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC;iBACvB;aACF;iBAAM;gBACL,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,CAAC;oBACvB,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC;aACJ;SACF;QACD,OAAO,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACH,2CAAe,GAAf,UAAgB,GAAQ,EAAE,IAAkB;QAC1C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,6CAAiB,GAAjB,UAAkB,GAAQ;QACxB,IAAI,CAAC,SAAS,CAAC,QAAM,CAAA,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,iCAAK,GAAL;QACE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;SACtB;QACD,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAEO,qCAAS,GAAjB;QACE,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;YAC/D,KAAkB,UAA+B,EAA/B,KAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAA/B,cAA+B,EAA/B,IAA+B,EAAE;gBAA9C,IAAM,GAAG,SAAA;gBACZ,IAAI,GAAG,IAAI,IAAI,EAAE;oBACf,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,EAAE;wBACtB,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;qBACrB;oBACD,IAAI,GAAG,CAAC,KAAK,IAAI,IAAI,EAAE;wBACrB,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBACxB,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC;qBACvB;iBACF;aACF;SACF;QACD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IACxB,CAAC;IAEO,qCAAS,GAAjB,UAAkB,OAAe;QAAjC,iBASC;QARC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;YAC/B,IAAI,KAAI,CAAC,OAAO,IAAI,IAAI,IAAI,KAAI,CAAC,OAAO,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE;gBACtE,KAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACpB,KAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,KAAI,CAAC,cAAc,GAAG,IAAI,CAAC;aAC5B;QACH,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;IAED,0CAAc,GAAd;QACE,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE;YACtE,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B,CAAC;IAEO,8CAAkB,GAA1B;QACE,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;YACrE,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC5B;IACH,CAAC;IAED;;;OAGG;IACH,yCAAa,GAAb,UAAc,YAAyB;QAAvC,iBAgFC;QA/EC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YAC5B,OAAQ,IAAY,CAAC,OAAO,CAAC;SAC9B;QACD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;QAC3B,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,YAAY,UAAU,EAAE;YAC7F,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC;SACzC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,aAAa,CAAC;QAExC,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,UAAC,CAAC;YACzB,IAAM,GAAG,GAAG,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,KAAI,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,KAAI,CAAC,SAAS,KAAK,UAAU,EAAE;gBACxE,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;aACrB;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;YACpB,KAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,KAAI,CAAC,mBAAmB,IAAI,SAAS,EAAE,KAAK,CAAC;iBACzE,IAAI,CAAC,UAAC,QAAQ;gBACb,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ,CAAC,KAAK,KAAK,CAAC,EAAE;oBACjD,WAAW;oBACX,IAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,wBAAwB,CAAQ,CAAC;oBACnE,IAAM,GAAG,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAC1C,IAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBACxC,KAAI,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC;oBACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBACtB,IAAI,CAAC,KAAI,CAAC,KAAK,CAAC,OAAO,EAAE;wBACvB,KAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;wBACxB,IAAI,KAAI,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,KAAI,CAAC,OAAO,KAAK,UAAU,EAAE;4BACpE,KAAI,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;yBAC9B;qBACF;iBACF;qBAAM;oBACL,IAAI,KAAI,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,KAAI,CAAC,OAAO,KAAK,UAAU,EAAE;wBACpE,KAAI,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;qBACrC;oBACD,KAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;oBACzB,KAAI,CAAC,KAAK,EAAE,CAAC;iBACd;YACH,CAAC,CAAC,CACD,OAAK,CAAA,CAAC,UAAC,KAAK;gBACX,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;gBACrC,IAAI,KAAI,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,KAAI,CAAC,OAAO,KAAK,UAAU,EAAE;oBACpE,KAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;iBAC5B;gBACD,KAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;gBACzB,KAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,UAAC,CAAC;YACvB,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACzB,IAAI,KAAI,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,KAAI,CAAC,OAAO,KAAK,UAAU,EAAE;gBACpE,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACjB;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,UAAC,CAAC;YACvB,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACzB,KAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,KAAI,CAAC,KAAK,CAAC,OAAO,EAAE;gBACvB,KAAI,CAAC,SAAS,CAAC,KAAI,CAAC,gBAAgB,CAAC,CAAC;aACvC;YACD,KAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;YAEzB,IAAI,KAAI,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,KAAI,CAAC,OAAO,KAAK,UAAU,EAAE;gBACpE,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACjB;QACH,CAAC,CAAC;QAEF,OAAO,IAAI,CAAC;IACd,CAAC;IACH,wBAAC;AAAD,CAAC,AAzUD,IAyUC"}