# 更新日志

## [1.0.0] - 2024-08-03

### 新增
- 🎉 首次发布 TypeScript 版本的 Futu API
- ✨ 完整的 TypeScript 类型支持
- 📦 ES 模块规范支持
- 🔧 现代化的构建工具链

### 特性
- **完整的 API 覆盖**: 支持所有交易和行情相关的 API
- **类型安全**: 提供完整的 TypeScript 类型定义
- **ES 模块**: 使用现代的 ES 模块规范
- **向后兼容**: 与原版 JavaScript API 保持兼容
- **完整测试**: 包含基础功能测试

### 技术栈
- TypeScript 5.0+
- ES2022 目标
- Node.js 18+
- protobufjs 7.x
- bytebuffer 5.x

### 包含的主要模块
- `FutuWebSocket`: 主要的 WebSocket 客户端类
- `FutuWebSocketBase`: 底层 WebSocket 基础类
- `ftCmdID`: 命令 ID 常量定义
- 完整的类型定义文件

### 支持的 API
#### 交易相关
- 获取账户列表 (GetAccList)
- 解锁交易 (UnlockTrade)
- 获取资金信息 (GetFunds)
- 获取持仓列表 (GetPositionList)
- 下单 (PlaceOrder)
- 修改订单 (ModifyOrder)
- 获取订单列表 (GetOrderList)
- 获取成交列表 (GetOrderFillList)

#### 行情相关
- 订阅行情 (Sub)
- 获取基本行情 (GetBasicQot)
- 获取K线数据 (GetKL)
- 获取分时数据 (GetRT)
- 获取买卖盘 (GetOrderBook)
- 获取逐笔数据 (GetTicker)

### 文档
- 📖 完整的使用指南
- 💡 示例代码
- 🔍 API 参考文档
