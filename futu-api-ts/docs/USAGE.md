# Futu API TypeScript 使用指南

## 安装

```bash
npm install futu-api-ts
```

## 基本使用

### 1. 导入模块

```typescript
import { FutuWebSocket, ftCmdID } from 'futu-api-ts';
```

### 2. 创建客户端实例

```typescript
const client = new FutuWebSocket();
```

### 3. 设置回调函数

```typescript
// 登录回调
client.onlogin = (success: boolean, message: any) => {
  if (success) {
    console.log('登录成功');
    // 在这里执行登录后的操作
  } else {
    console.error('登录失败:', message);
  }
};

// 推送数据回调
client.onPush = (cmd: number, data: any) => {
  console.log('收到推送数据:', cmd, data);
};
```

### 4. 连接到 Futu OpenD

```typescript
// 参数：IP地址, 端口, 是否使用SSL, API密钥(可选)
client.start('127.0.0.1', 11111, false, 'your-api-key');
```

## 交易相关 API

### 获取账户列表

```typescript
try {
  const result = await client.GetAccList({});
  console.log('账户列表:', result.s2c?.accList);
} catch (error) {
  console.error('获取账户列表失败:', error);
}
```

### 解锁交易

```typescript
const unlockReq = {
  c2s: {
    unlock: true,
    pwdMD5: 'your-password-md5-hash'
  }
};

try {
  const result = await client.UnlockTrade(unlockReq);
  console.log('解锁成功');
} catch (error) {
  console.error('解锁失败:', error);
}
```

### 获取资金信息

```typescript
const fundsReq = {
  c2s: {
    header: {
      trdEnv: 1, // 1=仿真环境, 0=真实环境
      accID: 123456, // 账户ID
      trdMarket: 1 // 1=港股, 2=美股
    }
  }
};

try {
  const result = await client.GetFunds(fundsReq);
  console.log('资金信息:', result.s2c?.funds);
} catch (error) {
  console.error('获取资金信息失败:', error);
}
```

### 获取持仓列表

```typescript
const positionReq = {
  c2s: {
    header: {
      trdEnv: 1,
      accID: 123456,
      trdMarket: 1
    }
  }
};

try {
  const result = await client.GetPositionList(positionReq);
  console.log('持仓列表:', result.s2c?.positionList);
} catch (error) {
  console.error('获取持仓列表失败:', error);
}
```

### 下单

```typescript
const orderReq = {
  c2s: {
    packetID: {
      connID: client.getConnID(),
      serialNo: Date.now()
    },
    header: {
      trdEnv: 1,
      accID: 123456,
      trdMarket: 1
    },
    trdSide: 1, // 1=买入, 2=卖出
    orderType: 1, // 1=限价单, 2=市价单
    code: '00700', // 股票代码
    qty: 100, // 数量
    price: 500.0 // 价格(限价单需要)
  }
};

try {
  const result = await client.PlaceOrder(orderReq);
  console.log('下单成功, 订单ID:', result.s2c?.orderID);
} catch (error) {
  console.error('下单失败:', error);
}
```

## 行情相关 API

### 订阅行情

```typescript
const subReq = {
  c2s: {
    securityList: [
      {
        market: 1, // 1=港股, 11=美股
        code: '00700' // 腾讯控股
      }
    ],
    subTypeList: [1], // 1=基本行情, 2=摆盘, 4=逐笔
    isSubOrUnSub: true, // true=订阅, false=取消订阅
    isRegOrUnRegPush: true // true=注册推送, false=取消推送
  }
};

try {
  const result = await client.Sub(subReq);
  console.log('订阅成功');
} catch (error) {
  console.error('订阅失败:', error);
}
```

### 获取基本行情

```typescript
const quotReq = {
  c2s: {
    securityList: [
      {
        market: 1,
        code: '00700'
      }
    ]
  }
};

try {
  const result = await client.GetBasicQot(quotReq);
  console.log('基本行情:', result.s2c?.qotList);
} catch (error) {
  console.error('获取行情失败:', error);
}
```

### 获取K线数据

```typescript
const klReq = {
  c2s: {
    security: {
      market: 1,
      code: '00700'
    },
    klType: 7, // 7=日K, 1=1分钟K线
    reqNum: 100 // 请求数量
  }
};

try {
  const result = await client.GetKL(klReq);
  console.log('K线数据:', result.s2c?.klList);
} catch (error) {
  console.error('获取K线失败:', error);
}
```

## 错误处理

所有 API 调用都返回 Promise，建议使用 try-catch 进行错误处理：

```typescript
try {
  const result = await client.GetAccList({});
  if (result.retType === 0) {
    // 成功
    console.log('操作成功:', result.s2c);
  } else {
    // 业务错误
    console.error('业务错误:', result.retMsg);
  }
} catch (error) {
  // 网络或其他错误
  console.error('请求失败:', error);
}
```

## 常用枚举值

### 市场类型 (Market)
- 1: 港股
- 11: 美股
- 21: 沪股
- 22: 深股

### 交易方向 (TrdSide)
- 1: 买入
- 2: 卖出

### 订单类型 (OrderType)
- 1: 限价单
- 2: 市价单

### 环境类型 (TrdEnv)
- 0: 真实环境
- 1: 仿真环境

## 注意事项

1. 使用前请确保 Futu OpenD 已启动并正确配置
2. API 密钥是可选的，但建议在生产环境中使用
3. 所有交易操作都需要先解锁交易
4. 订阅行情后会通过 onPush 回调接收推送数据
5. 请根据实际情况调整环境类型（真实/仿真）
