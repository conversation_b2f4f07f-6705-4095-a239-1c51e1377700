{"name": "futu-api-ts", "version": "1.0.0", "description": "Futu WebSocket API for Node.js with TypeScript support", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && cp src/proto.js dist/proto.js", "dev": "tsc --watch", "clean": "rm -rf dist", "prebuild": "npm run clean", "test": "node --test dist/test/*.js", "pretest": "npm run build", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["futu", "api", "futu-api", "futuopend", "typescript", "websocket", "trading"], "author": "Your Name", "license": "ISC", "dependencies": {"bytebuffer": "^5.0.1", "long": "^5.2.3", "protobufjs": "^7.2.5"}, "devDependencies": {"@types/bytebuffer": "^5.0.49", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}