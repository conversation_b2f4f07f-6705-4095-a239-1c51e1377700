{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "Node", "lib": ["ES2022", "DOM"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "isolatedModules": true, "verbatimModuleSyntax": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}