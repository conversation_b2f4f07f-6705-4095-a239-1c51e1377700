---
description: 'Enforce consistent spacing before function parenthesis.'
---

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/space-before-function-paren** for documentation.

This rule extends the base [`eslint/space-before-function-paren`](https://eslint.org/docs/rules/space-before-function-paren) rule.
It adds support for generic type parameters on function calls.
