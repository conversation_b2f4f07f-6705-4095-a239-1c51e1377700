{"name": "@types/bytebuffer", "version": "5.0.49", "description": "TypeScript definitions for bytebuffer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bytebuffer", "license": "MIT", "contributors": [], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/bytebuffer"}, "scripts": {}, "dependencies": {"@types/long": "^3.0.0", "@types/node": "*"}, "typesPublisherContentHash": "a554eadec561b7cd34e80b1969a29388428efb1108df16e3667c5470d4ce43da", "typeScriptVersion": "4.7"}