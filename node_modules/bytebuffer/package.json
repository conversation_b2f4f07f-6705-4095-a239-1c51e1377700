{"name": "bytebuffer", "version": "5.0.1", "author": "<PERSON> <<EMAIL>>", "description": "The swiss army knife for binary data in JavaScript.", "main": "dist/bytebuffer-node.js", "browser": "dist/bytebuffer.js", "repository": {"type": "git", "url": "https://github.com/dcodeIO/bytebuffer.js.git"}, "bugs": {"url": "https://github.com/dcodeIO/bytebuffer.js/issues"}, "keywords": ["net", "array", "buffer", "arraybuffer", "typed array", "bytebuffer", "json", "websocket", "webrtc"], "dependencies": {"long": "~3"}, "devDependencies": {"closurecompiler": "~1", "lxiv": "~0.2", "metascript": "~0", "pretty-hrtime": "^1.0.0", "testjs": "~1", "utfx": "^1.0.1"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}, "scripts": {"prepublish": "npm test", "test": "node node_modules/testjs/bin/testjs tests/suite.js", "make": "npm run-script build && npm run-script compile && npm run-script compress && npm test", "build": "node scripts/build.js", "compile": "npm run-script compile-default && npm run-script compile-dataview", "compile-default": "ccjs dist/bytebuffer.js --create_source_map=dist/bytebuffer.min.map --externs=externs/minimal-env.js --externs=node_modules/long/externs/long.js > dist/bytebuffer.min.js", "compile-dataview": "ccjs dist/bytebuffer-dataview.js --create_source_map=dist/bytebuffer-dataview.min.map --externs=externs/minimal-env.js --externs=node_modules/long/externs/long.js > dist/bytebuffer-dataview.min.js", "compress": "gzip -c -9 dist/bytebuffer.min.js > dist/bytebuffer.min.js.gz && gzip -c -9 dist/bytebuffer-dataview.min.js > dist/bytebuffer-dataview.min.js.gz"}}