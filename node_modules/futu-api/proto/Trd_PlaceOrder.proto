syntax = "proto2";
package Trd_PlaceOrder;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/trdplaceorder";

import "Common.proto";
import "Trd_Common.proto";

message C2S
{
	required Common.PacketID packetID = 1; //交易写操作防重放攻击
	required Trd_Common.TrdHeader header = 2; //交易公共参数头
	required int32 trdSide = 3; //交易方向, 参见Trd_Common.TrdSide的枚举定义
	required int32 orderType = 4; //订单类型, 参见Trd_Common.OrderType的枚举定义
	required string code = 5; //代码，港股必须是5位数字，A股必须是6位数字，美股没限制
	required double qty = 6; //数量，期权单位是"张"（精确到小数点后 0 位，超出部分会被舍弃。期权期货单位是"张"）
	optional double price = 7; //价格，（证券账户精确到小数点后 3 位，期货账户精确到小数点后 9 位，超出部分会被舍弃）
	//以下2个为调整价格使用，都传才有效，对港、A股有意义，因为港股有价位，A股2位精度，美股可不传
	optional bool adjustPrice = 8; //是否调整价格，如果价格不合法，是否调整到合法价位，true调整，false不调整
	optional double adjustSideAndLimit = 9; //调整方向和调整幅度百分比限制，正数代表向上调整，负数代表向下调整，具体值代表调整幅度限制，如：0.015代表向上调整且幅度不超过1.5%；-0.01代表向下调整且幅度不超过1%
	optional int32 secMarket = 10; //证券所属市场，参见TrdSecMarket的枚举定义
	optional string remark = 11; //用户备注字符串，最多只能传64字节。可用于标识订单唯一信息等，下单填上，订单结构就会带上。
	optional int32 timeInForce = 12; //订单有效期限，参见TrdCommon_TimeInForce的枚举定义
	optional bool fillOutsideRTH = 13; //是否允许盘前盘后成交。仅适用于美股限价单。默认false
	optional double auxPrice = 14; //触发价格
	optional int32 trailType = 15; //跟踪类型, 参见Trd_Common.TrailType的枚举定义
	optional double trailValue = 16; //跟踪金额/百分比
	optional double trailSpread = 17; //指定价差
	optional int32 session = 18; //美股订单时段, 参见Common.Session的枚举定义
}

message S2C
{
	required Trd_Common.TrdHeader header = 1; //交易公共参数头
	optional uint64 orderID = 2; //订单号
	optional string orderIDEx = 3; //表示服务器订单id，可以用来代替orderID，和orderID二选一
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	//以下3个字段每条协议都有，注释说明在InitConnect.proto中
	required int32 retType = 1 [default = -400];
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
