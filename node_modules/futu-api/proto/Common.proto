syntax = "proto2";
package Common;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/common";


//返回结果
enum RetType
{
	RetType_Succeed = 0; //成功
	RetType_Failed = -1; //失败
	RetType_TimeOut = -100; //超时
	RetType_DisConnect = -200; //连接断开
	RetType_Unknown = -400; //未知结果
	RetType_Invalid = -500; //包内容非法
}

//包加密算法
enum PacketEncAlgo
{
    PacketEncAlgo_FTAES_ECB = 0; //富途修改过的AES的ECB加密模式
	PacketEncAlgo_None = -1; //不加密
	PacketEncAlgo_AES_ECB = 1; //标准的AES的ECB加密模式
	PacketEncAlgo_AES_CBC = 2; //标准的AES的CBC加密模式
}

//包的唯一标识，用于回放攻击的识别和保护
message PacketID
{
	required uint64 connID = 1; //当前TCP连接的连接ID，一条连接的唯一标识，InitConnect协议会返回
	required uint32 serialNo = 2; //自增序列号
}

//协议格式，请求协议在请求头中指定，推送协议在Init时指定
enum ProtoFmt
{
	ProtoFmt_Protobuf = 0; //Google Protobuf格式
	ProtoFmt_Json = 1; //Json格式
}

//用户注册归属地
enum UserAttribution
{
	UserAttribution_Unknown = 0; //
	UserAttribution_NN = 1; //大陆
	UserAttribution_MM = 2; //MooMoo
	UserAttribution_SG = 3; //新加坡
	UserAttribution_AU = 4; //澳洲
	UserAttribution_JP = 5; //日本
	UserAttribution_HK = 6; //香港
}

enum ProgramStatusType
{
	ProgramStatusType_None = 0;
	ProgramStatusType_Loaded = 1; //已完成类似加载配置,启动服务器等操作,服务器启动之前的状态无需返回

	ProgramStatusType_Loging = 2; //登录中
	ProgramStatusType_NeedPicVerifyCode = 3; //需要图形验证码
	ProgramStatusType_NeedPhoneVerifyCode = 4; //需要手机验证码
	ProgramStatusType_LoginFailed = 5; //登录失败,详细原因在描述返回
	ProgramStatusType_ForceUpdate = 6; //客户端版本过低

	ProgramStatusType_NessaryDataPreparing = 7; //正在拉取类似免责声明等一些必要信息
	ProgramStatusType_NessaryDataMissing = 8; //缺少必要信息
	ProgramStatusType_UnAgreeDisclaimer = 9; //未同意免责声明
	ProgramStatusType_Ready = 10; //可以接收业务协议收发,正常可用状态
	
	//OpenD登录后被强制退出登录，会导致连接全部断开,需要重连后才能得到以下该状态（并且需要在ui模式下）
	ProgramStatusType_ForceLogout = 11; //被强制退出登录,例如修改了登录密码,中途打开设备锁等,详细原因在描述返回

	ProgramStatusType_DisclaimerPullFailed = 12; //拉取免责声明标志失败
}

message ProgramStatus
{
	required ProgramStatusType type = 1; //当前状态
	optional string strExtDesc = 2; // 额外描述
}

enum Session
{
	Session_NONE = 0;
	Session_RTH = 1;
	Session_ETH = 2;
	Session_ALL = 3;
	Session_OVERNIGHT = 4;
}