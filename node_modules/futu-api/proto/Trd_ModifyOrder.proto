syntax = "proto2";
package Trd_ModifyOrder;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/trdmodifyorder";

import "Common.proto";
import "Trd_Common.proto";

message C2S
{
	required Common.PacketID packetID = 1; //交易写操作防重放攻击
	required Trd_Common.TrdHeader header = 2; //交易公共参数头
	required uint64 orderID = 3; //订单号，forAll为true时，传0
	required int32 modifyOrderOp = 4; //修改操作类型，参见Trd_Common.ModifyOrderOp的枚举定义
	optional bool forAll = 5; //是否对此业务账户的全部订单操作，true是，false否(对单个订单)，无此字段代表false，仅对单个订单
	optional int32 trdMarket = 6; //指定市场，全部撤单时才需要
	
	//下面的字段仅针对单个订单，且modifyOrderOp为ModifyOrderOp_Normal有效
	optional double qty = 8; //数量，期权单位是"张"（精确到小数点后 0 位，超出部分会被舍弃）
	optional double price = 9; //价格，（证券账户精确到小数点后 3 位，期货账户精确到小数点后 9 位，超出部分会被舍弃）
	//以下为调整价格使用，都传才有效，对港、A股有意义，因为港股有价位，A股2位精度，美股可不传
	optional bool adjustPrice = 10; //是否调整价格，如果价格不合法，是否调整到合法价位，true调整，false不调整
	optional double adjustSideAndLimit = 11; //调整方向和调整幅度百分比限制，正数代表向上调整，负数代表向下调整，具体值代表调整幅度限制，如：0.015代表向上调整且幅度不超过1.5%；-0.01代表向下调整且幅度不超过1%
	optional double auxPrice = 12; //触发价格
	optional int32 trailType = 13; //跟踪类型, 参见Trd_Common.TrailType的枚举定义
	optional double trailValue = 14; //跟踪金额/百分比
	optional double trailSpread = 15; //指定价差
	optional string orderIDEx = 16; //表示服务器订单id，可以用来代替orderID，和orderID二选一
}

message S2C
{
	required Trd_Common.TrdHeader header = 1; //交易公共参数头
	required uint64 orderID = 2; //订单号
	optional string orderIDEx = 3; //服务器订单id
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	//以下3个字段每条协议都有，注释说明在InitConnect.proto中
	required int32 retType = 1 [default = -400];
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
