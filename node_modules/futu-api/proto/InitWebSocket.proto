syntax = "proto2";
package InitWebSocket;
message C2S
{   
   optional string IP = 1; //opend的ip
   optional int32 Port = 2;//opend的port
   optional string RSAPrivateKey = 3; //与opend连接的密钥（正文而非路径）
   optional string websocketKey = 4;
   optional string clientID = 5;
   optional string programmingLanguage = 6;
}

message S2C
{
	optional int64 serverTime = 1; //服务器回包时的格林威治时间戳，单位秒
	optional uint64 connID = 2; //连接ID
}

message Request
{
	optional C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;	
	optional S2C s2c = 4;
}
