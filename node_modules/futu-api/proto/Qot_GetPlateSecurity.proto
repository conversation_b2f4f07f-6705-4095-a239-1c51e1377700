syntax = "proto2";
package Qot_GetPlateSecurity;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotgetplatesecurity";

import "Common.proto";
import "Qot_Common.proto";

message C2S
{
	required Qot_Common.Security plate = 1; //板块
	optional int32 sortField = 2;//Qot_Common.SortField,根据哪个字段排序,不填默认Code排序
	optional bool ascend = 3;//升序ture, 降序false, 不填默认升序
}

message S2C
{
	repeated Qot_Common.SecurityStaticInfo staticInfoList = 1; //板块下的股票静态信息
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
