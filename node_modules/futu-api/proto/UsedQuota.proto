syntax = "proto2";
package UsedQuota;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/usedquota";

message C2S
{

}

message S2C
{
	optional int32 usedSubQuota = 1; // 已使用订阅额度
	optional int32 usedKLineQuota = 2; // 已使用历史K线额度
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
