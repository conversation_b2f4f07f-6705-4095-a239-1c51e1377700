syntax = "proto2";
package Qot_UpdatePriceReminder;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotupdatepricereminder";

import "Common.proto";
import "Qot_Common.proto";

message S2C
{
	required Qot_Common.Security security = 1; //股票
	optional string name = 11; // 股票名称
	required double price = 2; //价格
	required double changeRate = 3; //涨跌幅
	required int32 marketStatus = 4; //市场状态,Qot_Common::PriceReminderMarketStatus
	required string content = 5; //内容
	required string note = 6; //备注仅支持 20 个以内的中文字符
	optional int64 key = 7; // 到价提醒的标识
	optional int32 type = 8; // Qot_Common::PriceReminderType，提醒类型
	optional double setValue = 9; // 设置的提醒值
	optional double curValue = 10; // 设置的提醒类型触发时当前值
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
