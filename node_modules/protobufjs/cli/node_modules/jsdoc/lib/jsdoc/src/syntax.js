// TODO: docs
exports.Syntax = {
    ArrayExpression: 'ArrayExpression',
    ArrayPattern: 'ArrayPattern',
    ArrowFunctionExpression: 'ArrowFunctionExpression',
    AssignmentExpression: 'AssignmentExpression',
    AssignmentPattern: 'AssignmentPattern',
    AwaitExpression: 'AwaitExpression',
    BigIntLiteral: 'BigIntLiteral',
    BinaryExpression: 'BinaryExpression',
    BindExpression: 'BindExpression',
    BlockStatement: 'BlockStatement',
    BreakStatement: 'BreakStatement',
    CallExpression: 'CallExpression',
    CatchClause: 'CatchClause',
    ClassBody: 'ClassBody',
    ClassDeclaration: 'ClassDeclaration',
    ClassExpression: 'ClassExpression',
    ClassPrivateProperty: 'ClassPrivateProperty',
    ClassProperty: 'ClassProperty',
    ComprehensionBlock: 'ComprehensionBlock',
    ComprehensionExpression: 'ComprehensionExpression',
    ConditionalExpression: 'ConditionalExpression',
    ContinueStatement: 'ContinueStatement',
    DebuggerStatement: 'DebuggerStatement',
    Decorator: 'Decorator',
    DoExpression: 'DoExpression',
    DoWhileStatement: 'DoWhileStatement',
    EmptyStatement: 'EmptyStatement',
    ExperimentalRestProperty: 'ExperimentalRestProperty',
    ExperimentalSpreadProperty: 'ExperimentalSpreadProperty',
    ExportAllDeclaration: 'ExportAllDeclaration',
    ExportDefaultDeclaration: 'ExportDefaultDeclaration',
    ExportDefaultSpecifier: 'ExportDefaultSpecifier',
    ExportNamedDeclaration: 'ExportNamedDeclaration',
    ExportNamespaceSpecifier: 'ExportNamespaceSpecifier',
    ExportSpecifier: 'ExportSpecifier',
    ExpressionStatement: 'ExpressionStatement',
    File: 'File',
    ForInStatement: 'ForInStatement',
    ForOfStatement: 'ForOfStatement',
    ForStatement: 'ForStatement',
    FunctionDeclaration: 'FunctionDeclaration',
    FunctionExpression: 'FunctionExpression',
    Identifier: 'Identifier',
    IfStatement: 'IfStatement',
    Import: 'Import',
    ImportDeclaration: 'ImportDeclaration',
    ImportDefaultSpecifier: 'ImportDefaultSpecifier',
    ImportNamespaceSpecifier: 'ImportNamespaceSpecifier',
    ImportSpecifier: 'ImportSpecifier',
    JSXAttribute: 'JSXAttribute',
    JSXClosingElement: 'JSXClosingElement',
    JSXElement: 'JSXElement',
    JSXEmptyExpression: 'JSXEmptyExpression',
    JSXExpressionContainer: 'JSXExpressionContainer',
    JSXIdentifier: 'JSXIdentifier',
    JSXMemberExpression: 'JSXMemberExpression',
    JSXNamespacedName: 'JSXNamespacedName',
    JSXOpeningElement: 'JSXOpeningElement',
    JSXSpreadAttribute: 'JSXSpreadAttribute',
    JSXText: 'JSXText',
    LabeledStatement: 'LabeledStatement',
    LetStatement: 'LetStatement',
    Literal: 'Literal',
    LogicalExpression: 'LogicalExpression',
    MemberExpression: 'MemberExpression',
    MetaProperty: 'MetaProperty',
    MethodDefinition: 'MethodDefinition',
    NewExpression: 'NewExpression',
    ObjectExpression: 'ObjectExpression',
    ObjectPattern: 'ObjectPattern',
    PrivateName: 'PrivateName',
    Program: 'Program',
    Property: 'Property',
    RestElement: 'RestElement',
    ReturnStatement: 'ReturnStatement',
    SequenceExpression: 'SequenceExpression',
    SpreadElement: 'SpreadElement',
    Super: 'Super',
    SwitchCase: 'SwitchCase',
    SwitchStatement: 'SwitchStatement',
    TaggedTemplateExpression: 'TaggedTemplateExpression',
    TemplateElement: 'TemplateElement',
    TemplateLiteral: 'TemplateLiteral',
    ThisExpression: 'ThisExpression',
    ThrowStatement: 'ThrowStatement',
    TryStatement: 'TryStatement',
    UnaryExpression: 'UnaryExpression',
    UpdateExpression: 'UpdateExpression',
    VariableDeclaration: 'VariableDeclaration',
    VariableDeclarator: 'VariableDeclarator',
    WhileStatement: 'WhileStatement',
    WithStatement: 'WithStatement',
    YieldExpression: 'YieldExpression'
};
